<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.com.necloud</groupId>
        <artifactId>healthcar</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>health-car-server</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>API 及打包部署模块</description>

    <properties>
        <!-- ### 打包配置相关 ### -->
        <!-- 启动类 -->
        <main-class>cn.com.necloud.admin.HealthCarAdminApplication</main-class>
        <!-- 程序 jar 输出目录 -->
        <bin-path>bin/</bin-path>
        <!-- 配置文件输出目录 -->
        <config-path>config/</config-path>
        <!-- 依赖 jar 输出目录 -->
        <lib-path>lib/</lib-path>
    </properties>

    <dependencies>

        <!-- 系统管理模块 -->
        <dependency>
            <groupId>cn.com.necloud</groupId>
            <artifactId>health-car-system</artifactId>
        </dependency>



        <!-- 能力开放插件 -->
        <dependency>
            <groupId>cn.com.necloud</groupId>
            <artifactId>health-car-plugin-open</artifactId>
        </dependency>

        <!-- 代码生成器插件 -->
        <dependency>
            <groupId>cn.com.necloud</groupId>
            <artifactId>health-car-plugin-generator</artifactId>
        </dependency>

        <!-- ContiNew Starter 链路追踪模块 -->
        <dependency>
            <groupId>top.continew</groupId>
            <artifactId>continew-starter-trace</artifactId>
        </dependency>

        <!-- Liquibase（用于管理数据库版本，跟踪、管理和应用数据库变化） -->
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>

        <!-- PostgreSQL Java 驱动 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
            <version>8.0.33</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.parent.name}</finalName>
        <plugins>
            <!-- Spring Boot Maven 插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>${main-class}</mainClass>
                    <!-- 生成可执行的Fat JAR -->
                    <executable>true</executable>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- Maven 打包插件 - 注释掉以生成Fat JAR -->
            <!--
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <excludes>
                        <exclude>${config-path}</exclude>
                        <exclude>db/</exclude>
                        <exclude>templates/</exclude>
                        <exclude>logback-spring.xml</exclude>
                    </excludes>
                    <archive>
                        <manifest>
                            <mainClass>${main-class}</mainClass>
                            <classpathPrefix>../${lib-path}</classpathPrefix>
                            <addClasspath>true</addClasspath>
                            <useUniqueVersions>false</useUniqueVersions>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>../${config-path}</Class-Path>
                        </manifestEntries>
                    </archive>
                    <outputDirectory>${project.build.directory}/app/${bin-path}</outputDirectory>
                </configuration>
            </plugin>
            -->
            <!-- 拷贝依赖 jar - 注释掉以生成Fat JAR -->
            <!--
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/app/${lib-path}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            -->
            <!-- 拷贝配置文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>src/main/resources/${config-path}</directory>
                                </resource>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <includes>
                                        <include>db/</include>
                                        <include>templates/</include>
                                        <include>logback-spring.xml</include>
                                    </includes>
                                </resource>
                            </resources>
                            <outputDirectory>${project.build.directory}/app/${config-path}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>