# 产品信息导入模板说明

## 模板文件
- **文件名**: `product_configs.xlsx`
- **位置**: `health-car-system/src/main/resources/templates/import/`

## 模板字段说明

模板包含以下5个字段，用户只需填写这些业务字段：

| 列序号 | 字段名称 | 是否必填 | 数据类型 | 长度限制 | 说明 | 示例值 |
|--------|----------|----------|----------|----------|------|--------|
| A列(0) | API名称 | 是 | 文本 | 最大200字符 | API的名称标识 | 健康体检API |
| B列(1) | 备注信息 | 否 | 文本 | 最大500字符 | 相关说明信息 | 用于健康体检数据查询 |
| C列(2) | 产品类型标识 | 是 | 文本 | 最大50字符 | 产品类型的标识符 | HEALTH_CHECK |
| D列(3) | 商品价格(元) | 是 | 数字 | 大于0 | 商品价格，单位为元 | 10.50 |
| E列(4) | 是否上报标志 | 是 | 文本 | 1个字符 | Y表示上报，N表示不上报 | Y |

## 系统自动生成字段

以下字段由系统自动生成，**不需要**在模板中填写：
- **businessId**: 业务主键，系统自动生成UUID（去除-字符）
- **id**: 主键ID，数据库自动生成

## 重复数据处理

系统基于**API名称**检查重复数据，支持以下策略：
- **跳过该行**: 遇到重复的API名称时跳过该行数据
- **修改数据**: 遇到重复的API名称时更新现有数据

## 导入流程

1. **下载模板**: 通过API `GET /productconfig/productConfigs/import/template` 下载
2. **填写数据**: 按照模板格式填写5个业务字段
3. **解析数据**: 通过API `POST /productconfig/productConfigs/import/parse` 上传文件进行解析
4. **确认导入**: 通过API `POST /productconfig/productConfigs/import` 确认导入数据

## 注意事项

1. **表头不可修改**: 必须严格按照模板中的表头格式
2. **必填字段**: API名称、产品类型标识、商品价格、是否上报标志不能为空
3. **数据格式**: 商品价格必须为正数，是否上报标志只能填写Y或N
4. **唯一性**: API名称在系统中应该唯一，重复时根据策略处理
5. **字符限制**: 请注意各字段的长度限制

## 示例数据

```
API名称          | 备注信息           | 产品类型标识      | 商品价格(元) | 是否上报标志
健康体检API      | 用于健康体检数据查询 | HEALTH_CHECK     | 10.50       | Y
血压监测API      | 用于血压数据监测    | BLOOD_PRESSURE   | 8.00        | Y
心率监测API      | 用于心率数据监测    | HEART_RATE       | 6.50        | N
```
