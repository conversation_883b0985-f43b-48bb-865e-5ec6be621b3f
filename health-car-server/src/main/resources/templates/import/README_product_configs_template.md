# 产品信息导入模板说明

## 模板文件创建说明

由于无法直接创建Excel文件，请按照以下步骤手动创建产品信息导入模板：

### 1. 创建Excel文件
- 文件名：`product_configs.xlsx`
- 位置：`health-car-server/src/main/resources/templates/import/`

### 2. Excel表头设置
请在Excel第一行设置以下表头（严格按照顺序和名称）：

| A列 | B列 | C列 | D列 | E列 | F列 |
|-----|-----|-----|-----|-----|-----|
| 业务主键 | API名称 | 备注信息 | 产品类型标识 | 商品价格(元) | 是否上报标志 |

### 3. 示例数据
可以在第2-4行添加以下示例数据：

| 业务主键 | API名称 | 备注信息 | 产品类型标识 | 商品价格(元) | 是否上报标志 |
|---------|---------|---------|-------------|-------------|-------------|
| JKYC_001 | 健康体检API | 用于健康体检数据查询 | HEALTH_CHECK | 10.50 | Y |
| JKYC_002 | 血压监测API | 用于血压数据监测 | BLOOD_PRESSURE | 8.00 | Y |
| JKYC_003 | 心率监测API | 用于心率数据监测 | HEART_RATE | 6.50 | N |

### 4. 字段说明

- **业务主键**：必填，唯一标识，最大100个字符
- **API名称**：必填，API的名称，最大200个字符
- **备注信息**：可选，相关说明信息，最大500个字符
- **产品类型标识**：必填，产品类型的标识符，最大50个字符
- **商品价格(元)**：必填，数字格式，必须大于0
- **是否上报标志**：必填，Y表示上报，N表示不上报

### 5. 注意事项

1. 表头必须严格按照上述格式设置
2. 必填字段不能为空
3. 业务主键在系统中必须唯一
4. 商品价格必须为正数
5. 是否上报标志只能填写Y或N

### 6. 导入流程

1. 下载模板：通过API `GET /productconfig/productConfigs/import/template` 下载
2. 填写数据：按照模板格式填写产品信息
3. 解析数据：通过API `POST /productconfig/productConfigs/import/parse` 上传文件进行解析
4. 确认导入：通过API `POST /productconfig/productConfigs/import` 确认导入数据

### 7. 重复数据处理策略

- **跳过该行**：遇到重复的业务主键时跳过该行数据
- **修改数据**：遇到重复的业务主键时更新现有数据
- **停止导入**：遇到重复的业务主键时停止整个导入过程
