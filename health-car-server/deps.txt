[INFO] Scanning for projects...
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for top.continew:healthcar-server:jar:4.0.0-SNAPSHOT
[WARNING] 'build.plugins.plugin.version' for org.apache.maven.plugins:maven-compiler-plugin is missing. @ top.continew:healthcar-admin:${revision}, /Users/<USER>/IdeaProjects/continew_project/healthcar-admin/pom.xml, line 106, column 21
[WARNING] 'build.plugins.plugin.version' for org.apache.maven.plugins:maven-surefire-plugin is missing. @ top.continew:healthcar-admin:${revision}, /Users/<USER>/IdeaProjects/continew_project/healthcar-admin/pom.xml, line 114, column 21
[WARNING] 'build.plugins.plugin.version' for org.apache.maven.plugins:maven-jar-plugin is missing. @ top.continew:healthcar-server:${revision}, /Users/<USER>/IdeaProjects/continew_project/healthcar-admin/health-car-server/pom.xml, line 79, column 21
[WARNING] 'build.plugins.plugin.version' for org.apache.maven.plugins:maven-resources-plugin is missing. @ top.continew:healthcar-server:${revision}, /Users/<USER>/IdeaProjects/continew_project/healthcar-admin/health-car-server/pom.xml, line 125, column 21
[WARNING] 
[WARNING] It is highly recommended to fix these problems because they threaten the stability of your build.
[WARNING] 
[WARNING] For this reason, future Maven versions might no longer support building such malformed projects.
[WARNING] 
[INFO] 
[INFO] -------------------< top.continew:healthcar-server >-------------------
[INFO] Building healthcar-server 4.0.0-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ healthcar-server ---
[INFO] top.continew:healthcar-server:jar:4.0.0-SNAPSHOT
[INFO] +- top.continew:healthcar-system:jar:4.0.0-SNAPSHOT:compile
[INFO] |  +- top.continew:healthcar-common:jar:4.0.0-SNAPSHOT:compile
[INFO] |  |  +- me.ahoo.cosid:cosid-spring-boot-starter:jar:2.12.3:compile
[INFO] |  |  |  \- me.ahoo.cosid:cosid-core:jar:2.12.3:compile
[INFO] |  |  +- me.ahoo.cosid:cosid-spring-redis:jar:2.12.3:compile
[INFO] |  |  |  \- org.springframework.data:spring-data-redis:jar:3.3.11:compile
[INFO] |  |  |     +- org.springframework.data:spring-data-keyvalue:jar:3.3.11:compile
[INFO] |  |  |     +- org.springframework:spring-oxm:jar:6.1.19:compile
[INFO] |  |  |     \- org.springframework:spring-context-support:jar:6.1.19:compile
[INFO] |  |  +- org.dromara.x-file-storage:x-file-storage-spring:jar:2.2.1:compile
[INFO] |  |  |  \- org.dromara.x-file-storage:x-file-storage-core:jar:2.2.1:compile
[INFO] |  |  |     +- org.apache.tika:tika-core:jar:2.4.1:compile
[INFO] |  |  |     |  \- commons-io:commons-io:jar:2.17.0:compile
[INFO] |  |  |     \- net.coobird:thumbnailator:jar:0.4.20:compile
[INFO] |  |  +- com.amazonaws:aws-java-sdk-s3:jar:1.12.783:compile
[INFO] |  |  |  +- com.amazonaws:aws-java-sdk-kms:jar:1.12.783:compile
[INFO] |  |  |  +- com.amazonaws:aws-java-sdk-core:jar:1.12.783:compile
[INFO] |  |  |  |  +- commons-logging:commons-logging:jar:1.1.3:compile
[INFO] |  |  |  |  +- commons-codec:commons-codec:jar:1.16.1:compile
[INFO] |  |  |  |  +- org.apache.httpcomponents:httpclient:jar:4.5.14:compile
[INFO] |  |  |  |  |  \- org.apache.httpcomponents:httpcore:jar:4.4.16:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.17.3:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:jar:2.17.3:compile
[INFO] |  |  |  |  \- joda-time:joda-time:jar:2.12.7:compile
[INFO] |  |  |  \- com.amazonaws:jmespath-java:jar:1.12.783:compile
[INFO] |  |  +- org.freemarker:freemarker:jar:2.3.34:compile
[INFO] |  |  +- com.mysql:mysql-connector-j:jar:8.3.0:compile
[INFO] |  |  +- top.continew:continew-starter-extension-crud-mp:jar:2.12.2:compile
[INFO] |  |  |  +- top.continew:continew-starter-extension-crud-core:jar:2.12.2:compile
[INFO] |  |  |  |  +- top.continew:continew-starter-api-doc:jar:2.12.2:compile
[INFO] |  |  |  |  |  \- com.github.xiaoymin:knife4j-openapi3-jakarta-spring-boot-starter:jar:4.5.0:compile
[INFO] |  |  |  |  |     +- com.github.xiaoymin:knife4j-core:jar:4.5.0:compile
[INFO] |  |  |  |  |     +- com.github.xiaoymin:knife4j-openapi3-ui:jar:4.5.0:compile
[INFO] |  |  |  |  |     \- org.springdoc:springdoc-openapi-starter-webmvc-ui:jar:2.3.0:compile
[INFO] |  |  |  |  |        +- org.springdoc:springdoc-openapi-starter-webmvc-api:jar:2.3.0:compile
[INFO] |  |  |  |  |        |  \- org.springdoc:springdoc-openapi-starter-common:jar:2.3.0:compile
[INFO] |  |  |  |  |        |     \- io.swagger.core.v3:swagger-core-jakarta:jar:2.2.19:compile
[INFO] |  |  |  |  |        |        \- io.swagger.core.v3:swagger-models-jakarta:jar:2.2.19:compile
[INFO] |  |  |  |  |        \- org.webjars:swagger-ui:jar:5.10.3:compile
[INFO] |  |  |  |  +- top.continew:continew-starter-web:jar:2.12.2:compile
[INFO] |  |  |  |  |  +- org.springframework.boot:spring-boot-starter-web:jar:3.3.11:compile
[INFO] |  |  |  |  |  +- org.springframework.boot:spring-boot-starter-undertow:jar:3.3.11:compile
[INFO] |  |  |  |  |  |  +- io.undertow:undertow-core:jar:2.3.18.Final:compile
[INFO] |  |  |  |  |  |  |  +- org.jboss.xnio:xnio-api:jar:3.8.16.Final:compile
[INFO] |  |  |  |  |  |  |  |  +- org.wildfly.common:wildfly-common:jar:1.5.4.Final:compile
[INFO] |  |  |  |  |  |  |  |  \- org.wildfly.client:wildfly-client-config:jar:1.0.1.Final:compile
[INFO] |  |  |  |  |  |  |  +- org.jboss.xnio:xnio-nio:jar:3.8.16.Final:runtime
[INFO] |  |  |  |  |  |  |  \- org.jboss.threads:jboss-threads:jar:3.5.0.Final:compile
[INFO] |  |  |  |  |  |  +- io.undertow:undertow-servlet:jar:2.3.18.Final:compile
[INFO] |  |  |  |  |  |  +- io.undertow:undertow-websockets-jsr:jar:2.3.18.Final:compile
[INFO] |  |  |  |  |  |  |  +- jakarta.websocket:jakarta.websocket-api:jar:2.1.1:compile
[INFO] |  |  |  |  |  |  |  \- jakarta.websocket:jakarta.websocket-client-api:jar:2.1.1:compile
[INFO] |  |  |  |  |  |  \- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.40:compile
[INFO] |  |  |  |  |  \- com.feiniaojin:graceful-response:jar:5.0.5-boot3:compile
[INFO] |  |  |  |  |     \- org.springframework.boot:spring-boot-starter-validation:jar:3.3.11:compile
[INFO] |  |  |  |  +- top.continew:continew-starter-file-excel:jar:2.12.2:compile
[INFO] |  |  |  |  |  \- com.alibaba:easyexcel:jar:3.3.4:compile
[INFO] |  |  |  |  |     \- com.alibaba:easyexcel-core:jar:3.3.4:compile
[INFO] |  |  |  |  |        +- com.alibaba:easyexcel-support:jar:3.3.4:compile
[INFO] |  |  |  |  |        +- org.apache.poi:poi:jar:4.1.2:compile
[INFO] |  |  |  |  |        |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  |  |  |  |        |  \- com.zaxxer:SparseBitSet:jar:1.2:compile
[INFO] |  |  |  |  |        +- org.apache.poi:poi-ooxml:jar:4.1.2:compile
[INFO] |  |  |  |  |        |  +- org.apache.commons:commons-compress:jar:1.26.0:compile
[INFO] |  |  |  |  |        |  \- com.github.virtuald:curvesapi:jar:1.06:compile
[INFO] |  |  |  |  |        +- org.apache.poi:poi-ooxml-schemas:jar:4.1.2:compile
[INFO] |  |  |  |  |        |  \- org.apache.xmlbeans:xmlbeans:jar:3.1.0:compile
[INFO] |  |  |  |  |        +- org.apache.commons:commons-csv:jar:1.8:compile
[INFO] |  |  |  |  |        \- org.ehcache:ehcache:jar:3.10.8:compile
[INFO] |  |  |  |  |           \- org.glassfish.jaxb:jaxb-runtime:jar:4.0.5:runtime
[INFO] |  |  |  |  |              \- org.glassfish.jaxb:jaxb-core:jar:4.0.5:runtime
[INFO] |  |  |  |  |                 +- org.glassfish.jaxb:txw2:jar:4.0.5:runtime
[INFO] |  |  |  |  |                 \- com.sun.istack:istack-commons-runtime:jar:4.1.2:runtime
[INFO] |  |  |  |  \- cn.crane4j:crane4j-spring-boot-starter:jar:2.9.0:compile
[INFO] |  |  |  |     \- cn.crane4j:crane4j-extension-spring:jar:2.9.0:compile
[INFO] |  |  |  |        \- cn.crane4j:crane4j-core:jar:2.9.0:compile
[INFO] |  |  |  |           +- cn.crane4j:crane4j-annotation:jar:2.9.0:compile
[INFO] |  |  |  |           +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  |  |           \- ognl:ognl:jar:3.1.19:compile
[INFO] |  |  |  \- top.continew:continew-starter-data-mp:jar:2.12.2:compile
[INFO] |  |  |     +- com.baomidou:mybatis-plus-spring-boot3-starter:jar:3.5.8:compile
[INFO] |  |  |     |  +- com.baomidou:mybatis-plus:jar:3.5.8:compile
[INFO] |  |  |     |  +- org.mybatis:mybatis-spring:jar:3.0.4:compile
[INFO] |  |  |     |  +- com.baomidou:mybatis-plus-spring-boot-autoconfigure:jar:3.5.8:compile
[INFO] |  |  |     |  \- org.springframework.boot:spring-boot-starter-***********************
[INFO] |  |  |     |     +- com.zaxxer:HikariCP:jar:5.1.0:compile
[INFO] |  |  |     |     \- org.springframework:spring-***********************
[INFO] |  |  |     \- p6spy:p6spy:jar:3.9.1:compile
[INFO] |  |  +- top.continew:continew-starter-auth-satoken:jar:2.12.2:compile
[INFO] |  |  |  +- cn.dev33:sa-token-spring-boot3-starter:jar:1.42.0:compile
[INFO] |  |  |  |  +- cn.dev33:sa-token-jakarta-servlet:jar:1.42.0:compile
[INFO] |  |  |  |  \- cn.dev33:sa-token-spring-boot-autoconfig:jar:1.42.0:compile
[INFO] |  |  |  |     \- cn.dev33:sa-token-jackson:jar:1.42.0:compile
[INFO] |  |  |  +- cn.dev33:sa-token-jwt:jar:1.42.0:compile
[INFO] |  |  |  |  +- cn.dev33:sa-token-core:jar:1.42.0:compile
[INFO] |  |  |  |  \- cn.hutool:hutool-jwt:jar:5.8.37:compile
[INFO] |  |  |  \- cn.dev33:sa-token-redisson:jar:1.42.0:compile
[INFO] |  |  +- top.continew:continew-starter-auth-justauth:jar:2.12.2:compile
[INFO] |  |  |  +- me.zhyd.oauth:JustAuth:jar:1.16.7:compile
[INFO] |  |  |  |  +- com.xkcoding.http:simple-http:jar:1.0.5:compile
[INFO] |  |  |  |  \- com.alibaba:fastjson:jar:1.2.83:compile
[INFO] |  |  |  \- com.xkcoding.justauth:justauth-spring-boot-starter:jar:1.4.0:compile
[INFO] |  |  +- top.continew:continew-starter-cache-jetcache:jar:2.12.2:compile
[INFO] |  |  |  +- top.continew:continew-starter-cache-redisson:jar:2.12.2:compile
[INFO] |  |  |  |  \- org.redisson:redisson-spring-boot-starter:jar:3.46.0:compile
[INFO] |  |  |  |     +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.3.11:compile
[INFO] |  |  |  |     +- org.redisson:redisson:jar:3.46.0:compile
[INFO] |  |  |  |     |  +- javax.cache:cache-api:jar:1.1.1:compile
[INFO] |  |  |  |     |  +- io.projectreactor:reactor-core:jar:3.6.16:compile
[INFO] |  |  |  |     |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  |     |  +- io.reactivex.rxjava3:rxjava:jar:3.1.10:compile
[INFO] |  |  |  |     |  +- com.esotericsoftware:kryo:jar:4.0.3:compile
[INFO] |  |  |  |     |  |  \- com.esotericsoftware:minlog:jar:1.3.0:compile
[INFO] |  |  |  |     |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.17.3:compile
[INFO] |  |  |  |     |  \- org.jodd:jodd-util:jar:6.3.0:compile
[INFO] |  |  |  |     \- org.redisson:redisson-spring-data-34:jar:3.46.0:compile
[INFO] |  |  |  +- com.alicp.jetcache:jetcache-autoconfigure:jar:2.7.8:compile
[INFO] |  |  |  +- com.alicp.jetcache:jetcache-anno:jar:2.7.8:compile
[INFO] |  |  |  |  \- com.alicp.jetcache:jetcache-core:jar:2.7.8:compile
[INFO] |  |  |  |     +- com.alicp.jetcache:jetcache-anno-api:jar:2.7.8:compile
[INFO] |  |  |  |     +- com.alibaba.fastjson2:fastjson2:jar:2.0.51:compile
[INFO] |  |  |  |     \- com.github.ben-manes.caffeine:caffeine:jar:3.1.8:compile
[INFO] |  |  |  +- com.alicp.jetcache:jetcache-redisson:jar:2.7.8:compile
[INFO] |  |  |  \- org.apache.commons:commons-pool2:jar:2.12.1:compile
[INFO] |  |  +- top.continew:continew-starter-extension-datapermission-mp:jar:2.12.2:compile
[INFO] |  |  |  +- com.baomidou:mybatis-plus-extension:jar:3.5.8:compile
[INFO] |  |  |  |  \- com.baomidou:mybatis-plus-core:jar:3.5.8:compile
[INFO] |  |  |  |     +- com.baomidou:mybatis-plus-annotation:jar:3.5.8:compile
[INFO] |  |  |  |     +- com.github.jsqlparser:jsqlparser:jar:5.0:compile
[INFO] |  |  |  |     \- org.mybatis:mybatis:jar:3.5.16:compile
[INFO] |  |  |  +- top.continew:continew-starter-extension-datapermission-core:jar:2.12.2:compile
[INFO] |  |  |  \- top.continew:continew-starter-data-core:jar:2.12.2:compile
[INFO] |  |  |     +- cn.hutool:hutool-db:jar:5.8.37:compile
[INFO] |  |  |     \- org.springframework.data:spring-data-commons:jar:3.3.11:compile
[INFO] |  |  +- top.continew:continew-starter-messaging-websocket:jar:2.12.2:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-starter-websocket:jar:3.3.11:compile
[INFO] |  |  |     +- org.springframework:spring-messaging:jar:6.1.19:compile
[INFO] |  |  |     \- org.springframework:spring-websocket:jar:6.1.19:compile
[INFO] |  |  +- top.continew:continew-starter-messaging-mail:jar:2.12.2:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-starter-mail:jar:3.3.11:compile
[INFO] |  |  +- top.continew:continew-starter-captcha-graphic:jar:2.12.2:compile
[INFO] |  |  |  +- com.github.whvcse:easy-captcha:jar:1.6.2:compile
[INFO] |  |  |  \- org.openjdk.nashorn:nashorn-core:jar:15.6:compile
[INFO] |  |  |     +- org.ow2.asm:asm-commons:jar:7.3.1:compile
[INFO] |  |  |     |  \- org.ow2.asm:asm-analysis:jar:7.3.1:compile
[INFO] |  |  |     +- org.ow2.asm:asm-tree:jar:7.3.1:compile
[INFO] |  |  |     \- org.ow2.asm:asm-util:jar:7.3.1:compile
[INFO] |  |  +- top.continew:continew-starter-captcha-behavior:jar:2.12.2:compile
[INFO] |  |  |  \- com.anji-plus:captcha:jar:1.4.0:compile
[INFO] |  |  +- top.continew:continew-starter-ratelimiter:jar:2.12.2:compile
[INFO] |  |  +- top.continew:continew-starter-security-crypto:jar:2.12.2:compile
[INFO] |  |  |  \- cn.hutool:hutool-crypto:jar:5.8.37:compile
[INFO] |  |  +- top.continew:continew-starter-security-mask:jar:2.12.2:compile
[INFO] |  |  +- top.continew:continew-starter-security-password:jar:2.12.2:compile
[INFO] |  |  |  \- org.springframework.security:spring-security-crypto:jar:6.3.9:compile
[INFO] |  |  +- top.continew:continew-starter-json-jackson:jar:2.12.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.3.11:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.17.3:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.17.3:compile
[INFO] |  |  |  \- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.17.3:compile
[INFO] |  |  |     +- com.fasterxml.jackson.core:jackson-annotations:jar:2.17.3:compile
[INFO] |  |  |     \- com.fasterxml.jackson.core:jackson-core:jar:2.17.3:compile
[INFO] |  |  \- top.continew:continew-starter-log-interceptor:jar:2.12.2:compile
[INFO] |  |     \- top.continew:continew-starter-log-core:jar:2.12.2:compile
[INFO] |  |        +- io.swagger.core.v3:swagger-annotations-jakarta:jar:2.2.19:compile
[INFO] |  |        \- com.alibaba:transmittable-thread-local:jar:2.14.5:compile
[INFO] |  \- org.dromara.sms4j:sms4j-spring-boot-starter:jar:3.3.4:compile
[INFO] |     \- org.dromara.sms4j:sms4j-core:jar:3.3.4:compile
[INFO] |        \- org.dromara.sms4j:sms4j-provider:jar:3.3.4:compile
[INFO] |           +- org.dromara.sms4j:sms4j-api:jar:3.3.4:compile
[INFO] |           |  \- org.dromara.sms4j:sms4j-comm:jar:3.3.4:compile
[INFO] |           +- com.sun.xml.bind:jaxb-impl:jar:4.0.5:compile
[INFO] |           +- com.sun.xml.bind:jaxb-core:jar:4.0.5:compile
[INFO] |           \- javax.activation:activation:jar:1.1.1:compile
[INFO] +- top.continew:continew-plugin-schedule:jar:4.0.0-SNAPSHOT:compile
[INFO] |  +- com.aizuda:snail-job-client-starter:jar:1.4.0:compile
[INFO] |  |  \- org.springframework:spring-tx:jar:6.1.19:compile
[INFO] |  |     \- org.springframework:spring-beans:jar:6.1.19:compile
[INFO] |  +- com.aizuda:snail-job-client-retry-core:jar:1.4.0:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-aop:jar:3.3.11:compile
[INFO] |  |  |  +- org.springframework:spring-aop:jar:6.1.19:compile
[INFO] |  |  |  \- org.aspectj:aspectjweaver:jar:1.9.24:compile
[INFO] |  |  +- com.google.guava:guava:jar:33.3.0-jre:compile
[INFO] |  |  |  +- com.google.guava:failureaccess:jar:1.0.2:compile
[INFO] |  |  |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  |  |  +- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO] |  |  |  +- org.checkerframework:checker-qual:jar:3.43.0:compile
[INFO] |  |  |  \- com.google.j2objc:j2objc-annotations:jar:3.0.0:compile
[INFO] |  |  +- com.aizuda:snail-job-common-core:jar:1.4.0:compile
[INFO] |  |  |  +- jakarta.mail:jakarta.mail-api:jar:2.1.3:compile
[INFO] |  |  |  +- org.eclipse.angus:jakarta.mail:jar:2.0.3:compile
[INFO] |  |  |  |  \- org.eclipse.angus:angus-activation:jar:2.0.2:runtime
[INFO] |  |  |  +- io.netty:netty-common:jar:4.1.119.Final:compile
[INFO] |  |  |  +- io.grpc:grpc-protobuf:jar:1.66.0:compile
[INFO] |  |  |  |  \- io.grpc:grpc-protobuf-lite:jar:1.66.0:runtime
[INFO] |  |  |  +- io.grpc:grpc-stub:jar:1.66.0:compile
[INFO] |  |  |  |  \- com.google.errorprone:error_prone_annotations:jar:2.28.0:compile
[INFO] |  |  |  +- com.google.protobuf:protobuf-java:jar:3.25.3:compile
[INFO] |  |  |  +- com.google.api.grpc:proto-google-common-protos:jar:2.41.0:compile
[INFO] |  |  |  \- com.aizuda:snail-job-common-log:jar:1.4.0:compile
[INFO] |  |  +- com.aizuda:snail-job-common-server-api:jar:1.4.0:compile
[INFO] |  |  +- com.aizuda:snail-job-common-client-api:jar:1.4.0:compile
[INFO] |  |  \- com.aizuda:snail-job-client-common:jar:1.4.0:compile
[INFO] |  |     +- io.netty:netty-all:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-buffer:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-codec:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-codec-dns:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-codec-haproxy:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-codec-http:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-codec-http2:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-codec-memcache:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-codec-mqtt:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-codec-redis:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-codec-smtp:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-codec-socks:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-codec-stomp:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-codec-xml:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-handler:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-handler-proxy:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-handler-ssl-ocsp:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-resolver:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-resolver-dns:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-transport:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-transport-rxtx:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-transport-sctp:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-transport-udt:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-transport-classes-epoll:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-transport-classes-kqueue:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-resolver-dns-classes-macos:jar:4.1.119.Final:compile
[INFO] |  |     |  +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.119.Final:runtime
[INFO] |  |     |  +- io.netty:netty-transport-native-epoll:jar:linux-aarch_64:4.1.119.Final:runtime
[INFO] |  |     |  +- io.netty:netty-transport-native-epoll:jar:linux-riscv64:4.1.119.Final:runtime
[INFO] |  |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-x86_64:4.1.119.Final:runtime
[INFO] |  |     |  +- io.netty:netty-transport-native-kqueue:jar:osx-aarch_64:4.1.119.Final:runtime
[INFO] |  |     |  +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.119.Final:runtime
[INFO] |  |     |  \- io.netty:netty-resolver-dns-native-macos:jar:osx-aarch_64:4.1.119.Final:runtime
[INFO] |  |     +- com.github.rholder:guava-retrying:jar:2.0.0:compile
[INFO] |  |     +- io.grpc:grpc-netty-shaded:jar:1.66.0:compile
[INFO] |  |     |  +- io.grpc:grpc-core:jar:1.66.0:compile
[INFO] |  |     |  |  +- com.google.code.gson:gson:jar:2.10.1:runtime
[INFO] |  |     |  |  +- com.google.android:annotations:jar:4.1.1.4:runtime
[INFO] |  |     |  |  \- io.grpc:grpc-context:jar:1.66.0:runtime
[INFO] |  |     |  \- io.perfmark:perfmark-api:jar:0.27.0:runtime
[INFO] |  |     +- io.grpc:grpc-api:jar:1.66.0:compile
[INFO] |  |     \- io.grpc:grpc-util:jar:1.66.0:compile
[INFO] |  |        \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.24:runtime
[INFO] |  +- com.aizuda:snail-job-client-job-core:jar:1.4.0:compile
[INFO] |  |  +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.5.3.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  \- org.springframework.cloud:spring-cloud-starter-openfeign:jar:4.1.4:compile
[INFO] |     +- org.springframework.cloud:spring-cloud-starter:jar:4.1.5:compile
[INFO] |     |  +- org.springframework.cloud:spring-cloud-context:jar:4.1.5:compile
[INFO] |     |  \- org.springframework.security:spring-security-rsa:jar:1.1.3:compile
[INFO] |     |     \- org.bouncycastle:bcprov-jdk18on:jar:1.78:compile
[INFO] |     +- org.springframework.cloud:spring-cloud-openfeign-core:jar:4.1.4:compile
[INFO] |     |  \- io.github.openfeign:feign-form-spring:jar:13.5:compile
[INFO] |     |     +- io.github.openfeign:feign-form:jar:13.5:compile
[INFO] |     |     \- commons-fileupload:commons-fileupload:jar:1.5:compile
[INFO] |     +- org.springframework:spring-web:jar:6.1.19:compile
[INFO] |     |  \- io.micrometer:micrometer-observation:jar:1.13.13:compile
[INFO] |     |     \- io.micrometer:micrometer-commons:jar:1.13.13:compile
[INFO] |     +- org.springframework.cloud:spring-cloud-commons:jar:4.1.5:compile
[INFO] |     +- io.github.openfeign:feign-core:jar:13.5:compile
[INFO] |     \- io.github.openfeign:feign-slf4j:jar:13.5:compile
[INFO] +- top.continew:continew-plugin-open:jar:4.0.0-SNAPSHOT:compile
[INFO] +- top.continew:continew-plugin-generator:jar:4.0.0-SNAPSHOT:compile
[INFO] +- top.continew:continew-starter-trace:jar:2.12.2:compile
[INFO] |  +- top.continew:continew-starter-core:jar:2.12.2:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-configuration-processor:jar:3.3.11:compile
[INFO] |  |  +- org.springframework:spring-webmvc:jar:6.1.19:compile
[INFO] |  |  |  +- org.springframework:spring-context:jar:6.1.19:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:6.1.19:compile
[INFO] |  |  +- jakarta.servlet:jakarta.servlet-api:jar:6.0.0:compile
[INFO] |  |  +- net.dreamlu:mica-ip2region:jar:3.3.6:compile
[INFO] |  |  |  \- net.dreamlu:mica-core:jar:3.3.6:compile
[INFO] |  |  +- cn.hutool:hutool-core:jar:5.8.37:compile
[INFO] |  |  +- cn.hutool:hutool-json:jar:5.8.37:compile
[INFO] |  |  +- cn.hutool:hutool-extra:jar:5.8.37:compile
[INFO] |  |  |  \- cn.hutool:hutool-setting:jar:5.8.37:compile
[INFO] |  |  |     \- cn.hutool:hutool-log:jar:5.8.37:compile
[INFO] |  |  \- cn.hutool:hutool-http:jar:5.8.37:compile
[INFO] |  \- com.yomahub:tlog-web-spring-boot-starter:jar:1.5.2:compile
[INFO] |     +- com.yomahub:tlog-spring-boot-configuration:jar:1.5.2:compile
[INFO] |     +- com.yomahub:tlog-webroot:jar:1.5.2:compile
[INFO] |     |  \- com.yomahub:tlog-core:jar:1.5.2:compile
[INFO] |     |     +- org.dom4j:dom4j:jar:2.1.3:compile
[INFO] |     |     +- org.javassist:javassist:jar:3.25.0-GA:compile
[INFO] |     |     \- com.alibaba:QLExpress:jar:3.3.1:compile
[INFO] |     |        +- commons-beanutils:commons-beanutils:jar:1.9.4:compile
[INFO] |     |        |  \- commons-collections:commons-collections:jar:3.2.2:compile
[INFO] |     |        \- commons-lang:commons-lang:jar:2.4:compile
[INFO] |     +- com.yomahub:tlog-task:jar:1.5.2:compile
[INFO] |     +- com.yomahub:tlog-httpclient:jar:1.5.2:compile
[INFO] |     |  \- com.yomahub:tlog-common:jar:1.5.2:compile
[INFO] |     +- com.yomahub:tlog-okhttp:jar:1.5.2:compile
[INFO] |     |  \- com.squareup.okhttp3:okhttp:jar:4.12.0:compile
[INFO] |     |     +- com.squareup.okio:okio:jar:3.6.0:compile
[INFO] |     |     |  \- com.squareup.okio:okio-jvm:jar:3.6.0:compile
[INFO] |     |     |     \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.9.25:compile
[INFO] |     |     \- org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.9.25:compile
[INFO] |     |        +- org.jetbrains.kotlin:kotlin-stdlib:jar:1.9.25:compile
[INFO] |     |        |  \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |     |        \- org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:1.9.25:compile
[INFO] |     +- com.yomahub:tlog-hutool-http:jar:1.5.2:compile
[INFO] |     +- com.yomahub:tlog-forest:jar:1.5.2:compile
[INFO] |     \- com.yomahub:tlog-rest-template:jar:1.5.2:compile
[INFO] +- org.liquibase:liquibase-core:jar:4.27.0:compile
[INFO] |  +- com.opencsv:opencsv:jar:5.9:compile
[INFO] |  +- org.apache.commons:commons-text:jar:1.11.0:compile
[INFO] |  +- org.apache.commons:commons-collections4:jar:4.4:compile
[INFO] |  +- org.yaml:snakeyaml:jar:2.4:compile
[INFO] |  +- javax.xml.bind:jaxb-api:jar:2.3.1:compile
[INFO] |  \- org.apache.commons:commons-lang3:jar:3.14.0:compile
[INFO] +- org.springframework.boot:spring-boot-starter-test:jar:3.3.11:test
[INFO] |  +- org.springframework.boot:spring-boot-starter:jar:3.3.11:compile
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.3.11:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.3.11:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:3.3.11:compile
[INFO] |  |  |  +- ch.qos.logback:logback-classic:jar:1.5.18:compile
[INFO] |  |  |  |  \- ch.qos.logback:logback-core:jar:1.5.18:compile
[INFO] |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.23.1:compile
[INFO] |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.23.1:compile
[INFO] |  |  |  \- org.slf4j:jul-to-slf4j:jar:2.0.17:compile
[INFO] |  |  \- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] |  +- org.springframework.boot:spring-boot-test:jar:3.3.11:test
[INFO] |  +- org.springframework.boot:spring-boot-test-autoconfigure:jar:3.3.11:test
[INFO] |  +- com.jayway.jsonpath:json-path:jar:2.9.0:test
[INFO] |  |  \- org.slf4j:slf4j-api:jar:2.0.17:compile
[INFO] |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:compile
[INFO] |  |  \- jakarta.activation:jakarta.activation-api:jar:2.1.3:compile
[INFO] |  +- net.minidev:json-smart:jar:2.5.2:test
[INFO] |  |  \- net.minidev:accessors-smart:jar:2.5.2:test
[INFO] |  |     \- org.ow2.asm:asm:jar:9.7.1:compile
[INFO] |  +- org.assertj:assertj-core:jar:3.25.3:test
[INFO] |  |  \- net.bytebuddy:byte-buddy:jar:1.14.19:compile
[INFO] |  +- org.awaitility:awaitility:jar:4.2.2:test
[INFO] |  +- org.hamcrest:hamcrest:jar:2.2:test
[INFO] |  +- org.junit.jupiter:junit-jupiter:jar:5.10.5:test
[INFO] |  |  +- org.junit.jupiter:junit-jupiter-api:jar:5.10.5:test
[INFO] |  |  |  +- org.opentest4j:opentest4j:jar:1.3.0:test
[INFO] |  |  |  +- org.junit.platform:junit-platform-commons:jar:1.10.5:test
[INFO] |  |  |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] |  |  +- org.junit.jupiter:junit-jupiter-params:jar:5.10.5:test
[INFO] |  |  \- org.junit.jupiter:junit-jupiter-engine:jar:5.10.5:test
[INFO] |  |     \- org.junit.platform:junit-platform-engine:jar:1.10.5:test
[INFO] |  +- org.mockito:mockito-core:jar:5.11.0:test
[INFO] |  |  +- net.bytebuddy:byte-buddy-agent:jar:1.14.19:test
[INFO] |  |  \- org.objenesis:objenesis:jar:3.3:compile
[INFO] |  +- org.mockito:mockito-junit-jupiter:jar:5.11.0:test
[INFO] |  +- org.skyscreamer:jsonassert:jar:1.5.3:test
[INFO] |  |  \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:test
[INFO] |  +- org.springframework:spring-core:jar:6.1.19:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:6.1.19:compile
[INFO] |  +- org.springframework:spring-test:jar:6.1.19:test
[INFO] |  \- org.xmlunit:xmlunit-core:jar:2.9.1:test
[INFO] +- cn.hutool:hutool-all:jar:5.8.37:compile
[INFO] \- org.projectlombok:lombok:jar:1.18.38:compile (optional)
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  1.126 s
[INFO] Finished at: 2025-06-24T16:03:58+08:00
[INFO] ------------------------------------------------------------------------
