const crypto = require('crypto');

// RSA公钥
const publicKey = `-----BEGIN PUBLIC KEY-----
MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAM51dgYtMyF+tTQt80sfFOpSV27a7t9u
aUVeFrdGiVxscuizE7H8SMntYqfn9lp8a5GH5P1/GGehVjUD2gF/4kcCAwEAAQ==
-----END PUBLIC KEY-----`;

// 要加密的密码
const password = 'admin123';

// 使用RSA公钥加密
const encrypted = crypto.publicEncrypt(publicKey, Buffer.from(password));
const encryptedBase64 = encrypted.toString('base64');

console.log('加密后的密码:', encryptedBase64);
