<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.necloud.admin.system.mapper.RoleMenuMapper">
    <select id="selectMenuIdByRoleIds" resultType="java.lang.Long">
        SELECT menu_id
        FROM sys_role_menu
        WHERE role_id IN
        <foreach collection="list" item="roleId" open="(" close=")" separator=",">
            #{roleId}
        </foreach>
    </select>
</mapper>