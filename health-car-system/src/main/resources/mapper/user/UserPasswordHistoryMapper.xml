<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.necloud.admin.system.mapper.user.UserPasswordHistoryMapper">
    <delete id="deleteExpired">
        DELETE t1 FROM sys_user_password_history AS t1
        LEFT JOIN (
            SELECT id
            FROM sys_user_password_history
            WHERE user_id = #{userId}
            ORDER BY create_time DESC
            LIMIT #{count}
        ) AS t2 ON t2.id = t1.id
        WHERE t2.id IS NULL
    </delete>
</mapper>