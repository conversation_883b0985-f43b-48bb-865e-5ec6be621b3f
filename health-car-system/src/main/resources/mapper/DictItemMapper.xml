<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.necloud.admin.system.mapper.DictItemMapper">
    <select id="listByDictCode" resultType="top.continew.starter.extension.crud.model.resp.LabelValueResp">
        SELECT t1.label, t1.value, t1.color AS extra
        FROM sys_dict_item AS t1
        LEFT JOIN sys_dict AS t2 ON t1.dict_id = t2.id
        WHERE t1.status = 1 AND t2.code = #{dictCode}
        ORDER BY t1.sort
    </select>
</mapper>