<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.necloud.admin.bussiness.bussinesscall.mapper.SupplierInterfaceRecordsMapper">
    <select id="selectDataSourceStatistics" resultType="cn.com.necloud.admin.statistics.model.resp.DataStatisticsResp">
        SELECT supplier_name,
               api_name,
               SUM(h.aaaa) AS total
        FROM (
                 SELECT supplier_name,
                        CASE
                            WHEN api_name LIKE '%出险信息%' THEN '出险信息'
                            WHEN api_name LIKE '%精准维保%' THEN '精准维保'
                            WHEN api_name LIKE '%vin解析%' THEN 'vin解析'
                            WHEN api_name LIKE '%异常里程%' THEN '异常里程'
                            ELSE api_name
                            END AS api_name,
                        SUM(
                                CASE
                                    WHEN supplier_name = '车传真' AND callback_result LIKE '%\"sign\":1%' THEN 1
                                    WHEN supplier_name = '第一车网' AND is_enabled = '1' THEN 1
                                    WHEN supplier_name = '瓦谷' AND is_enabled = '200' THEN 1
                                    WHEN supplier_name = '鹏海' AND (
                                        (api_name!='' AND is_enabled='true') OR
                                        (api_name = '异常里程' AND response_content LIKE '%\"status\":true%')
                                        ) THEN 1
                                    WHEN supplier_name = '精友' AND response_content LIKE '%成功%' THEN 1
                                    ELSE 0
                                    END
                        ) AS aaaa
                 FROM (
                          SELECT s.supplier_name,
                                 s.api_name,
                                 s.response_content,
                                 s.order_number,
                                 c.is_enabled,
                                 c.callback_result
                          FROM hc_supplier_interface_records s
                                   LEFT JOIN hc_api_callback_records c ON s.supplier_order_number = c.order_id
                          WHERE s.create_time <![CDATA[ >= ]]>#{starttime}
                            AND s.create_time <![CDATA[ < ]]>#{endtime}
                      ) AS b
                 GROUP BY supplier_name, api_name
             ) AS h
        GROUP BY supplier_name, api_name
    </select>
</mapper>