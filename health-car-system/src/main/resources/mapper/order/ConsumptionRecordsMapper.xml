<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.necloud.admin.order.mapper.ConsumptionRecordsMapper">
<select id="selectTableReccord" resultType="cn.com.necloud.admin.statistics.model.resp.RevenueStatisticsRecordResp">
    select  user_id,count(*) as totalamount,sum(case when payment_status = '1' then 1 else 0  end)as  successmount, sum(case when  payment_status!='1'    then 1 else 0  end)as failmount,sum(case when payment_status = '1' then payment_amount else 0  end)as revenue,sum(0) as gross  from hc_consumption_records ${ew.customSqlSegment} group by user_id order by totalamount desc
</select>
</mapper>