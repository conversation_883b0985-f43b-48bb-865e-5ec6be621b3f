<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.necloud.admin.bussiness.order.mapper.BusinessUserApiOrderRecordsMapper">
    <select id="selectStatisticsPage" resultType="cn.com.necloud.admin.statistics.model.resp.StatisticsApiRecordsDetailResp">
        SELECT
        customer_id as userName,
        COUNT(*) AS apiCount,
        SUM(CASE WHEN query_success_flag = '1' THEN 1 ELSE 0 END) AS pushSuccessCount,
        SUM(CASE WHEN query_success_flag = '2' THEN 1 ELSE 0 END) AS pushFailCount,
        SUM(CASE WHEN callback_timeout = '1' THEN 1 ELSE 0 END) AS thirdPartyCallbackTimeoutCount,
        SUM(CASE WHEN recall_success_flag = '1' THEN 1 ELSE 0 END) AS thirdPartyCallbackSuccessCount,
        SUM(CASE WHEN health_record_success_flag = '1' THEN 1 ELSE 0 END) AS healthRecordHasCount,
        ROUND(SUM(CASE WHEN query_success_flag = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS pushSuccessRate,
        ROUND(SUM(CASE WHEN query_success_flag = '2' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS pushFailRate,
        ROUND(SUM(CASE WHEN callback_timeout = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS thirdPartyCallbackTimeoutRate,
        ROUND(SUM(CASE WHEN recall_success_flag = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS thirdPartyCallbackSuccessRate,
        ROUND(SUM(CASE WHEN health_record_success_flag = '1' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) AS healthRecordNoRate
        FROM hc_business_user_api_order_records
        ${ew.customSqlSegment}
    </select>

<!-- jkyccustomer hc_business_users查询除含税之外的收支管理列表jkycapiorder hc_business_user_api_order_records-->
<!--    <select id="selectStatisticsList" resultType="cn.com.necloud.admin.statistics.model.resp.RevenueStatisticsRecordResp">-->
<!--        select a.*,hc_business_users.customer_name from  ( select  customer_id,api_name,remarks,count(*) as totalamount, sum(case when query_success_flag = '1' then 1 else 0  end)as  successmount,-->
<!--        sum(case when  query_success_flag ='2' or  query_success_flag is null   then 1 else 0  end)as failmount,sum(consumption_amount)as revenue,sum(cost_price)as costprice,-->
<!--        sum(0) as gross  from  hc_business_user_api_order_records where customer_id !='jkycxcx' and order_number not in (select order_number from hc_user_order_refund_records ${ew.customSqlSegment} ) group by customer_id,api_name,remarks  ) as a LEFT join hc_business_users  on a.customer_id =hc_business_users.customer_id  order by a.totalamount desc-->

<!--    </select>-->

    <select id="selectStatisticsList" resultType="cn.com.necloud.admin.statistics.model.resp.RevenueStatisticsRecordResp">
        SELECT a.*, hc_business_users.customer_name
        FROM (
        SELECT
        o.customer_id,
        o.api_name,
        o.remarks,
        COUNT(*) AS totalamount,
        SUM(CASE WHEN o.query_success_flag = '1' THEN 1 ELSE 0 END) AS successmount,
        SUM(CASE WHEN o.query_success_flag = '2' OR o.query_success_flag IS NULL THEN 1 ELSE 0 END) AS failmount,
        SUM(o.consumption_amount) AS revenue,
        SUM(o.cost_price) AS costprice,
        SUM(0) AS gross
        FROM hc_business_user_api_order_records o
        WHERE o.customer_id != 'jkycxcx'
        AND o.customer_id IN
        <foreach collection="customerIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND o.order_number NOT IN (
        SELECT r.order_number
        FROM hc_user_order_refund_records r
        WHERE r.customer_id IN
        <foreach collection="customerIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="ew != null and ew.sqlSegment != null and ew.sqlSegment != ''">
            AND ${ew.sqlSegment}
        </if>
        )
        GROUP BY o.customer_id, o.api_name, o.remarks
        ) AS a
        LEFT JOIN hc_business_users ON a.customer_id = hc_business_users.customer_id
        ORDER BY a.totalamount DESC
        /* 使用MyBatis-Plus分页方言 */
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomerIdsPage" resultType="string">
        SELECT customer_id FROM hc_business_user_api_order_records
        WHERE customer_id != 'jkycxcx'
        <if test="cratetime != null and cratetime != ''">
            AND create_time <![CDATA[ >= ]]> #{cratetime}
        </if>
        <if test="customerId != null and customerId != ''">
            AND customer_id = #{customerId}
        </if>
        GROUP BY customer_id
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <!-- 添加总数查询 -->
    <select id="countCustomerIds" resultType="long">
        SELECT COUNT(DISTINCT customer_id) FROM hc_business_user_api_order_records
        WHERE customer_id != 'jkycxcx'
        <if test="cratetime != null and cratetime != ''">
            AND create_time <![CDATA[ >= ]]> #{cratetime}
        </if>
        <if test="customerId != null and customerId != ''">
            AND customer_id = #{customerId}
        </if>
    </select>
    <select id="selectOrderRecords" resultType="cn.com.necloud.admin.statistics.model.resp.DataStatisticsResp">
        SELECT
        remarks,
        api_name,
        SUM(count) AS total
        FROM (
        SELECT
        CASE
        WHEN remarks IS NULL OR remarks = '' THEN '鹏海'
        ELSE remarks
        END AS remarks,
        CASE
        WHEN api_name LIKE '%异常里程%' THEN '异常里程'
        WHEN api_name LIKE '%普通维保%' THEN '普通维保'
        WHEN api_name LIKE '%品牌维保%' THEN '品牌维保'
        WHEN api_name LIKE '%精准维保%' THEN '精准维保'
        WHEN api_name LIKE '%出险信息%' THEN '出险信息'
        ELSE api_name
        END AS api_name,
        SUM(
        CASE
        WHEN (api_name LIKE '%出险信息%' OR api_name LIKE '%精准维保%')
        AND recall_content LIKE '%\"status\":\"1\"%' THEN 1
        WHEN (api_name NOT LIKE '%出险信息%' AND api_name NOT LIKE '%精准维保%') THEN 1
        ELSE 0
        END
        ) AS count
        FROM hc_business_user_api_order_records
        WHERE create_time <![CDATA[ > ]]>#{cratetime}
        AND create_time <![CDATA[ <= ]]> #{endtime}
        AND data_type_identifier = '1'
        AND (remarks = '' OR remarks IS NULL OR remarks = '鹏海')
        AND api_response LIKE '%\"status\":true%'
        GROUP BY api_name, remarks
        ) AS a
        GROUP BY remarks, api_name
    </select>


</mapper>