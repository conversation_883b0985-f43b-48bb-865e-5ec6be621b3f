<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.necloud.admin.statistics.mapper.ApiQueriesMapper">

    <select id="selectApi" resultType="cn.com.necloud.admin.statistics.model.resp.DataStatisticsResp">
        SELECT
            api_query_name,
            SUM(count) AS total
        FROM (
                 SELECT
                     CASE
                         WHEN api_query_name LIKE '%附加%' THEN '附加查询'
                         ELSE api_query_name
                         END AS api_query_name,
                     COUNT(*) AS count
                 FROM hc_api_queries
                 WHERE create_time <![CDATA[ >= ]]> #{cratetime, jdbcType=VARCHAR}
                   AND create_time <![CDATA[ <= ]]> #{endtime, jdbcType=VARCHAR}
                   AND response_result LIKE '%"status":true%'
                 GROUP BY api_query_name
             ) b
        GROUP BY api_query_name
    </select>
</mapper>