SET @parentId = 1945315960515534848;
-- 发票管理管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, '发票管理管理', 1000, 2, '/invoice/invoiceManagementRecords', 'InvoiceManagementRecords', 'invoice/invoiceManagementRecords/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- 发票管理管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1945315960515534849, '列表', @parentId, 3, 'invoice:invoiceManagementRecords:list', 1, 1, 1, NOW()),
    (1945315960515534850, '详情', @parentId, 3, 'invoice:invoiceManagementRecords:get', 2, 1, 1, NOW()),
    (1945315960515534851, '新增', @parentId, 3, 'invoice:invoiceManagementRecords:create', 3, 1, 1, NOW()),
    (1945315960515534852, '修改', @parentId, 3, 'invoice:invoiceManagementRecords:update', 4, 1, 1, NOW()),
    (1945315960515534853, '删除', @parentId, 3, 'invoice:invoiceManagementRecords:delete', 5, 1, 1, NOW()),
    (1945315960515534854, '导出', @parentId, 3, 'invoice:invoiceManagementRecords:export', 6, 1, 1, NOW());

