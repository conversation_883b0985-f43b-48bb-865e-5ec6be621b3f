SET @parentId = 1943507874661253120;
-- 健康用车api推送错误记录表管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, '健康用车api推送错误记录表管理', 1000, 2, '/pusherrrecord/apiPushErrorLog', 'ApiPushErrorLog', 'pusherrrecord/apiPushErrorLog/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- 健康用车api推送错误记录表管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1943507874661253121, '列表', @parentId, 3, 'pusherrrecord:apiPushErrorLog:list', 1, 1, 1, NOW()),
    (1943507874661253122, '详情', @parentId, 3, 'pusherrrecord:apiPushErrorLog:get', 2, 1, 1, NOW()),
    (1943507874661253123, '新增', @parentId, 3, 'pusherrrecord:apiPushErrorLog:create', 3, 1, 1, NOW()),
    (1943507874661253124, '修改', @parentId, 3, 'pusherrrecord:apiPushErrorLog:update', 4, 1, 1, NOW()),
    (1943507874661253125, '删除', @parentId, 3, 'pusherrrecord:apiPushErrorLog:delete', 5, 1, 1, NOW()),
    (1943507874661253126, '导出', @parentId, 3, 'pusherrrecord:apiPushErrorLog:export', 6, 1, 1, NOW());

