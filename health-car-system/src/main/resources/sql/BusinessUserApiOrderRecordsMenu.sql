SET @parentId = 1943265479895269376;
-- 商业用户 api 调用订单表管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, '商业用户 api 调用订单表管理', 1000, 2, '/order/businessUserApiOrderRecords', 'BusinessUserApiOrderRecords', 'order/businessUserApiOrderRecords/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- 商业用户 api 调用订单表管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1943265479895269377, '列表', @parentId, 3, 'order:businessUserApiOrderRecords:list', 1, 1, 1, NOW()),
    (1943265479895269378, '详情', @parentId, 3, 'order:businessUserApiOrderRecords:get', 2, 1, 1, NOW()),
    (1943265479895269379, '新增', @parentId, 3, 'order:businessUserApiOrderRecords:create', 3, 1, 1, NOW()),
    (1943265479895269380, '修改', @parentId, 3, 'order:businessUserApiOrderRecords:update', 4, 1, 1, NOW()),
    (1943265479895269381, '删除', @parentId, 3, 'order:businessUserApiOrderRecords:delete', 5, 1, 1, NOW()),
    (1943265479895269382, '导出', @parentId, 3, 'order:businessUserApiOrderRecords:export', 6, 1, 1, NOW());

