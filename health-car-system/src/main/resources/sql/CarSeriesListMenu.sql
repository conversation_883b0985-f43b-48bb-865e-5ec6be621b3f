SET @parentId = 1943563082266615808;
-- 车型库车系管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, '车型库车系管理', 1000, 2, '/base/carSeriesList', 'CarSeriesList', 'base/carSeriesList/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- 车型库车系管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1943563082266615809, '列表', @parentId, 3, 'base:carSeriesList:list', 1, 1, 1, NOW()),
    (1943563082266615810, '详情', @parentId, 3, 'base:carSeriesList:get', 2, 1, 1, NOW()),
    (1943563082266615811, '新增', @parentId, 3, 'base:carSeriesList:create', 3, 1, 1, NOW()),
    (1943563082266615812, '修改', @parentId, 3, 'base:carSeriesList:update', 4, 1, 1, NOW()),
    (1943563082266615813, '删除', @parentId, 3, 'base:carSeriesList:delete', 5, 1, 1, NOW()),
    (1943563082266615814, '导出', @parentId, 3, 'base:carSeriesList:export', 6, 1, 1, NOW());

