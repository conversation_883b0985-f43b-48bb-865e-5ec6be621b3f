SET @parentId = 1945329981549498368;
-- 用户订单退款记录管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, '用户订单退款记录管理', 1000, 2, '/refund/userOrderRefundRecords', 'UserOrderRefundRecords', 'refund/userOrderRefundRecords/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- 用户订单退款记录管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1945329981549498369, '列表', @parentId, 3, 'refund:userOrderRefundRecords:list', 1, 1, 1, NOW()),
    (1945329981549498370, '详情', @parentId, 3, 'refund:userOrderRefundRecords:get', 2, 1, 1, NOW()),
    (1945329981549498371, '新增', @parentId, 3, 'refund:userOrderRefundRecords:create', 3, 1, 1, NOW()),
    (1945329981549498372, '修改', @parentId, 3, 'refund:userOrderRefundRecords:update', 4, 1, 1, NOW()),
    (1945329981549498373, '删除', @parentId, 3, 'refund:userOrderRefundRecords:delete', 5, 1, 1, NOW()),
    (1945329981549498374, '导出', @parentId, 3, 'refund:userOrderRefundRecords:export', 6, 1, 1, NOW());

