SET @parentId = 1947124461952475136;
-- api查询结果管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, 'api查询结果管理', 1000, 2, '/statistics/apiQueries', 'ApiQueries', 'statistics/apiQueries/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- api查询结果管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1947124461952475137, '列表', @parentId, 3, 'statistics:apiQueries:list', 1, 1, 1, NOW()),
    (1947124461952475138, '详情', @parentId, 3, 'statistics:apiQueries:get', 2, 1, 1, NOW()),
    (1947124461952475139, '新增', @parentId, 3, 'statistics:apiQueries:create', 3, 1, 1, NOW()),
    (1947124461952475140, '修改', @parentId, 3, 'statistics:apiQueries:update', 4, 1, 1, NOW()),
    (1947124461952475141, '删除', @parentId, 3, 'statistics:apiQueries:delete', 5, 1, 1, NOW()),
    (1947124461952475142, '导出', @parentId, 3, 'statistics:apiQueries:export', 6, 1, 1, NOW());

