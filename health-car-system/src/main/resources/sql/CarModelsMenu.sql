SET @parentId = 1944594263607164928;
-- 车型列表管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, '车型列表管理', 1000, 2, '/base/carModels', 'CarModels', 'base/carModels/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- 车型列表管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1944594263607164929, '列表', @parentId, 3, 'base:carModels:list', 1, 1, 1, NOW()),
    (1944594263607164930, '详情', @parentId, 3, 'base:carModels:get', 2, 1, 1, NOW()),
    (1944594263607164931, '新增', @parentId, 3, 'base:carModels:create', 3, 1, 1, NOW()),
    (1944594263607164932, '修改', @parentId, 3, 'base:carModels:update', 4, 1, 1, NOW()),
    (1944594263607164933, '删除', @parentId, 3, 'base:carModels:delete', 5, 1, 1, NOW()),
    (1944594263607164934, '导出', @parentId, 3, 'base:carModels:export', 6, 1, 1, NOW());

