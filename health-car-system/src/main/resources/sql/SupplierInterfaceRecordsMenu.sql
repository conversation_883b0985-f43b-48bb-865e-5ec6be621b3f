SET @parentId = 1943559908965957632;
-- 厂商接口调用记录表管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, '厂商接口调用记录表管理', 1000, 2, '/bussinesscall/supplierInterfaceRecords', 'SupplierInterfaceRecords', 'bussinesscall/supplierInterfaceRecords/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- 厂商接口调用记录表管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1943559908965957633, '列表', @parentId, 3, 'bussinesscall:supplierInterfaceRecords:list', 1, 1, 1, NOW()),
    (1943559908965957634, '详情', @parentId, 3, 'bussinesscall:supplierInterfaceRecords:get', 2, 1, 1, NOW()),
    (1943559908965957635, '新增', @parentId, 3, 'bussinesscall:supplierInterfaceRecords:create', 3, 1, 1, NOW()),
    (1943559908965957636, '修改', @parentId, 3, 'bussinesscall:supplierInterfaceRecords:update', 4, 1, 1, NOW()),
    (1943559908965957637, '删除', @parentId, 3, 'bussinesscall:supplierInterfaceRecords:delete', 5, 1, 1, NOW()),
    (1943559908965957638, '导出', @parentId, 3, 'bussinesscall:supplierInterfaceRecords:export', 6, 1, 1, NOW());

