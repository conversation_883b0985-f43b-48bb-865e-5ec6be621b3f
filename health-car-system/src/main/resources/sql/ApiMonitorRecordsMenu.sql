SET @parentId = 1944944478042882048;
-- api监听记录管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, 'api监听记录管理', 1000, 2, '/apimonitor/apiMonitorRecords', 'ApiMonitorRecords', 'apimonitor/apiMonitorRecords/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- api监听记录管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1944944478042882049, '列表', @parentId, 3, 'apimonitor:apiMonitorRecords:list', 1, 1, 1, NOW()),
    (1944944478042882050, '详情', @parentId, 3, 'apimonitor:apiMonitorRecords:get', 2, 1, 1, NOW()),
    (1944944478042882051, '新增', @parentId, 3, 'apimonitor:apiMonitorRecords:create', 3, 1, 1, NOW()),
    (1944944478042882052, '修改', @parentId, 3, 'apimonitor:apiMonitorRecords:update', 4, 1, 1, NOW()),
    (1944944478042882053, '删除', @parentId, 3, 'apimonitor:apiMonitorRecords:delete', 5, 1, 1, NOW()),
    (1944944478042882054, '导出', @parentId, 3, 'apimonitor:apiMonitorRecords:export', 6, 1, 1, NOW());

