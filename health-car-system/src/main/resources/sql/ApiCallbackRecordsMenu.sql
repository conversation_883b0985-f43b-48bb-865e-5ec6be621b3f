SET @parentId = 1943483083729580032;
-- 健康用车api回调接口记录管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, '健康用车api回调接口记录管理', 1000, 2, '/apicallback/apiCallbackRecords', 'ApiCallbackRecords', 'apicallback/apiCallbackRecords/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- 健康用车api回调接口记录管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1943483083729580033, '列表', @parentId, 3, 'apicallback:apiCallbackRecords:list', 1, 1, 1, NOW()),
    (1943483083729580034, '详情', @parentId, 3, 'apicallback:apiCallbackRecords:get', 2, 1, 1, NOW()),
    (1943483083729580035, '新增', @parentId, 3, 'apicallback:apiCallbackRecords:create', 3, 1, 1, NOW()),
    (1943483083729580036, '修改', @parentId, 3, 'apicallback:apiCallbackRecords:update', 4, 1, 1, NOW()),
    (1943483083729580037, '删除', @parentId, 3, 'apicallback:apiCallbackRecords:delete', 5, 1, 1, NOW()),
    (1943483083729580038, '导出', @parentId, 3, 'apicallback:apiCallbackRecords:export', 6, 1, 1, NOW());

