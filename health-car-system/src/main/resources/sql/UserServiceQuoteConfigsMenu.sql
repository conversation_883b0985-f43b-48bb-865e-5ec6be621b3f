SET @parentId = 1945322272896917504;
-- 用户服务报价配置表管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, '用户服务报价配置表管理', 1000, 2, '/groupservice/userServiceQuoteConfigs', 'UserServiceQuoteConfigs', 'groupservice/userServiceQuoteConfigs/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- 用户服务报价配置表管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1945322272896917505, '列表', @parentId, 3, 'groupservice:userServiceQuoteConfigs:list', 1, 1, 1, NOW()),
    (1945322272896917506, '详情', @parentId, 3, 'groupservice:userServiceQuoteConfigs:get', 2, 1, 1, NOW()),
    (1945322272896917507, '新增', @parentId, 3, 'groupservice:userServiceQuoteConfigs:create', 3, 1, 1, NOW()),
    (1945322272901111808, '修改', @parentId, 3, 'groupservice:userServiceQuoteConfigs:update', 4, 1, 1, NOW()),
    (1945322272901111809, '删除', @parentId, 3, 'groupservice:userServiceQuoteConfigs:delete', 5, 1, 1, NOW()),
    (1945322272901111810, '导出', @parentId, 3, 'groupservice:userServiceQuoteConfigs:export', 6, 1, 1, NOW());

