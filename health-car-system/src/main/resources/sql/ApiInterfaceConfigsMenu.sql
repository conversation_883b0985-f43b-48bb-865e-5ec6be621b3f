SET @parentId = 1943554312363520000;
-- api接口配置表管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, 'api接口配置表管理', 1000, 2, '/apiconfig/apiInterfaceConfigs', 'ApiInterfaceConfigs', 'apiconfig/apiInterfaceConfigs/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- api接口配置表管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1943554312363520001, '列表', @parentId, 3, 'apiconfig:apiInterfaceConfigs:list', 1, 1, 1, NOW()),
    (1943554312363520002, '详情', @parentId, 3, 'apiconfig:apiInterfaceConfigs:get', 2, 1, 1, NOW()),
    (1943554312363520003, '新增', @parentId, 3, 'apiconfig:apiInterfaceConfigs:create', 3, 1, 1, NOW()),
    (1943554312363520004, '修改', @parentId, 3, 'apiconfig:apiInterfaceConfigs:update', 4, 1, 1, NOW()),
    (1943554312363520005, '删除', @parentId, 3, 'apiconfig:apiInterfaceConfigs:delete', 5, 1, 1, NOW()),
    (1943554312363520006, '导出', @parentId, 3, 'apiconfig:apiInterfaceConfigs:export', 6, 1, 1, NOW());

