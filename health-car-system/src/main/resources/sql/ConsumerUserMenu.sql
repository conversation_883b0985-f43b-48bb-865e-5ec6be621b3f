SET @parentId = 1945373532634181632;
-- 消费用户表管理菜单
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `path`, `name`, `component`, `redirect`, `icon`, `is_external`, `is_cache`, `is_hidden`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (@parentId, '消费用户表管理', 1000, 2, '/consumer/consumerUser', 'ConsumerUser', 'consumer/consumerUser/index', NULL, NULL, b'0', b'0', b'0', NULL, 1, 1, 1, NOW());

-- 消费用户表管理按钮
INSERT INTO `sys_menu`
    (`id`, `title`, `parent_id`, `type`, `permission`, `sort`, `status`, `create_user`, `create_time`)
VALUES
    (1945373532634181633, '列表', @parentId, 3, 'consumer:consumerUser:list', 1, 1, 1, NOW()),
    (1945373532634181634, '详情', @parentId, 3, 'consumer:consumerUser:get', 2, 1, 1, NOW()),
    (1945373532634181635, '新增', @parentId, 3, 'consumer:consumerUser:create', 3, 1, 1, NOW()),
    (1945373532634181636, '修改', @parentId, 3, 'consumer:consumerUser:update', 4, 1, 1, NOW()),
    (1945373532634181637, '删除', @parentId, 3, 'consumer:consumerUser:delete', 5, 1, 1, NOW()),
    (1945373532634181638, '导出', @parentId, 3, 'consumer:consumerUser:export', 6, 1, 1, NOW());

