/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.organizational.service;

import cn.com.necloud.admin.organizational.model.query.OrganizationQuery;
import cn.com.necloud.admin.organizational.model.req.OrganizationReq;
import cn.com.necloud.admin.organizational.model.resp.OrganizationDetailResp;
import cn.com.necloud.admin.organizational.model.resp.OrganizationResp;
import top.continew.starter.extension.crud.service.BaseService;

/**
 * 汽修机构表业务接口
 *
 * <AUTHOR>
 * @since 2025/07/10 17:32
 */
public interface OrganizationService extends BaseService<OrganizationResp, OrganizationDetailResp, OrganizationQuery, OrganizationReq> {}