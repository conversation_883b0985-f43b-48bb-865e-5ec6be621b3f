/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.order.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 消费记录表实体
 *
 * <AUTHOR>
 * @since 2025/07/10 14:45
 */
@Data
@TableName("hc_consumption_records")
public class ConsumptionRecordsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键，jkycxfjltable表唯一标识
     */
    private String businessId;

    /**
     * 用户唯一标识
     */
    private String userId;

    /**
     * 消费类型
     */
    private String consumptionType;

    /**
     * 查询类型
     */
    private String queryType;

    /**
     * 车辆识别代码
     */
    private String vin;

    /**
     * 许可证文件地址
     */
    private String licenseFileUrl;

    /**
     * 支付状态（0-未支付，1-已支付）
     */
    private String paymentStatus;

    /**
     * 报告地址
     */
    private String reportUrl;

    /**
     * 支付金额，单位：元
     */
    private String payAmount;

    /**
     * 支付金额，单位：元
     */
    private BigDecimal paymentAmount;

    /**
     * 微信用户唯一标识
     */
    private String wechatOpenId;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 查询时间
     */
    private LocalDateTime queryTime;

    /**
     * 查询订单编号
     */
    private String queryOrderNumber;

    /**
     * 报告状态
     */
    private String reportStatus;

    /**
     * 测试字段
     */
    private BigDecimal testField;

    /**
     * 认证状态（0-未认证，1-已认证）
     */
    private String authenticationStatus;

    /**
     * 认证时间
     */
    private LocalDateTime authenticationTime;

    /**
     * 车牌号码
     */
    private String vehicleLicensePlateNumber;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 参数名称
     */
    private String parameterName;

    /**
     * 渠道来源
     */
    private String channelSource;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 支付免单次数
     */
    private String paymentFreeCount;

    /**
     * 报告数据
     */
    private String reportData;

    /**
     * 拒绝原因
     */
    private String rejectionReason;

    /**
     * 车辆类型名称
     */
    private String vehicleTypeName;

    /**
     * 召回时间
     */
    private LocalDateTime recallTime;
}
