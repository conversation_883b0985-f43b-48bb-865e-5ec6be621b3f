/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.order.controller;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import cn.com.necloud.admin.common.controller.BaseController;
import cn.com.necloud.admin.bussiness.order.model.query.BusinessUserApiOrderRecordsQuery;
import cn.com.necloud.admin.bussiness.order.model.req.BusinessUserApiOrderRecordsReq;
import cn.com.necloud.admin.bussiness.order.model.resp.BusinessUserApiOrderRecordsDetailResp;
import cn.com.necloud.admin.bussiness.order.model.resp.BusinessUserApiOrderRecordsResp;
import cn.com.necloud.admin.bussiness.order.service.BusinessUserApiOrderRecordsService;

/**
 * 商业用户 api 调用订单表管理 API
 *
 * <AUTHOR>
 * @since 2025/07/10 19:06
 */
@Tag(name = "商业用户 api 调用订单表管理 API")
@RestController
@CrudRequestMapping(value = "/order/businessUserApiOrderRecords", api = {Api.PAGE, Api.GET, Api.CREATE, Api.UPDATE,
    Api.DELETE, Api.EXPORT})
public class BusinessUserApiOrderRecordsController extends BaseController<BusinessUserApiOrderRecordsService, BusinessUserApiOrderRecordsResp, BusinessUserApiOrderRecordsDetailResp, BusinessUserApiOrderRecordsQuery, BusinessUserApiOrderRecordsReq> {}