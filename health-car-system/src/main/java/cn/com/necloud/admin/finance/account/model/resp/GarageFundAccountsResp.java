/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.account.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 汽修资金账户表信息
 *
 * <AUTHOR>
 * @since 2025/07/15 15:55
 */
@Data
@Schema(description = "汽修资金账户表信息")
public class GarageFundAccountsResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键，唯一标识记录
     */
    @Schema(description = "业务主键，唯一标识记录")
    private String businessId;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名")
    private String updateUser;

    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除标记（0-未删除，1-已删除）
     */
    @Schema(description = "是否删除标记（0-未删除，1-已删除）")
    private Integer isDeleted;

    /**
     * 用户唯一标识
     */
    @Schema(description = "用户唯一标识")
    private String userId;

    /**
     * 账户名称
     */
    @Schema(description = "账户名称")
    private String accountName;

    /**
     * 账户类型
     */
    @Schema(description = "账户类型")
    private String accountType;

    /**
     * 余额，单位：元
     */
    @Schema(description = "余额，单位：元")
    private BigDecimal balanceAmount;

    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String contactName;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactPhone;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    private Integer version;

    /**
     * 机构名称
     */
    @Schema(description = "机构名称")
    private String organizationName;

    /**
     * 地区编号
     */
    @Schema(description = "地区编号")
    private BigDecimal areaId;

    /**
     * 机构唯一标识
     */
    @Schema(description = "机构唯一标识")
    private String organizationId;

    /**
     * 支付密码
     */
    @Schema(description = "支付密码")
    private String payPassword;
}