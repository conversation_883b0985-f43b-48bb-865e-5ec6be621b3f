/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.productconfig.controller;

import cn.com.necloud.admin.bussiness.productconfig.model.req.ProductConfigsImportReq;
import cn.com.necloud.admin.bussiness.productconfig.model.resp.ProductConfigsImportParseResp;
import cn.com.necloud.admin.bussiness.productconfig.model.resp.ProductConfigsImportResp;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import top.continew.starter.core.validation.ValidationUtils;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import cn.com.necloud.admin.common.controller.BaseController;
import cn.com.necloud.admin.bussiness.productconfig.model.query.ProductConfigsQuery;
import cn.com.necloud.admin.bussiness.productconfig.model.req.ProductConfigsReq;
import cn.com.necloud.admin.bussiness.productconfig.model.resp.ProductConfigsDetailResp;
import cn.com.necloud.admin.bussiness.productconfig.model.resp.ProductConfigsResp;
import cn.com.necloud.admin.bussiness.productconfig.service.ProductConfigsService;

import java.io.IOException;

/**
 * 产品信息管理管理 API
 *
 * <AUTHOR>
 * @since 2025/07/11 15:01
 */
@Tag(name = "产品信息管理管理 API")
@RestController
@CrudRequestMapping(value = "/productconfig/productConfigs", api = {Api.PAGE, Api.GET, Api.CREATE, Api.UPDATE,
    Api.DELETE, Api.EXPORT})
public class ProductConfigsController extends BaseController<ProductConfigsService, ProductConfigsResp, ProductConfigsDetailResp, ProductConfigsQuery, ProductConfigsReq> {

    @Operation(summary = "下载导入模板", description = "下载导入模板")
    @SaCheckPermission("productconfig:productConfigs:import")
    @GetMapping(value = "/import/template", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void downloadImportTemplate(HttpServletResponse response) throws IOException {
        baseService.downloadImportTemplate(response);
    }

    @Operation(summary = "解析导入数据", description = "解析导入数据")
    @SaCheckPermission("productconfig:productConfigs:import")
    @PostMapping("/import/parse")
    public ProductConfigsImportParseResp parseImport(@NotNull(message = "文件不能为空") @RequestPart MultipartFile file) {
        ValidationUtils.throwIf(file::isEmpty, "文件不能为空");
        return baseService.parseImport(file);
    }

    @Operation(summary = "导入数据", description = "导入数据")
    @SaCheckPermission("productconfig:productConfigs:import")
    @PostMapping("/import")
    public ProductConfigsImportResp importData(@RequestBody ProductConfigsImportReq importReq) {
        return baseService.importData(importReq);
    }
}