/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.invoice.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import cn.com.necloud.admin.common.model.resp.NewBaseDetailResp;
import java.io.Serial;
import java.time.*;

/**
 * 发票管理详情信息
 *
 * <AUTHOR>
 * @since 2025/07/16 10:54
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "发票管理详情信息")
public class InvoiceManagementRecordsDetailResp extends NewBaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键ID
     */
    @Schema(description = "业务主键ID")
    @ExcelProperty(value = "业务主键ID")
    private String businessId;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    @ExcelProperty(value = "用户名")
    private String userName;

    /**
     * 发票抬头
     */
    @Schema(description = "发票抬头")
    @ExcelProperty(value = "发票抬头")
    private String invoiceTitle;

    /**
     * 发票金额
     */
    @Schema(description = "发票金额")
    @ExcelProperty(value = "发票金额")
    private String invoiceAmount;

    /**
     * 收件人姓名
     */
    @Schema(description = "收件人姓名")
    @ExcelProperty(value = "收件人姓名")
    private String recipientName;

    /**
     * 账单手机号
     */
    @Schema(description = "账单手机号")
    @ExcelProperty(value = "账单手机号")
    private String billPhoneNumber;

    /**
     * 快递公司名称
     */
    @Schema(description = "快递公司名称")
    @ExcelProperty(value = "快递公司名称")
    private String expressCompanyName;

    /**
     * 快递单号
     */
    @Schema(description = "快递单号")
    @ExcelProperty(value = "快递单号")
    private String expressOrderNumber;

    /**
     * 账单地址
     */
    @Schema(description = "账单地址")
    @ExcelProperty(value = "账单地址")
    private String billAddress;

    /**
     * 发票状态（0-未开票，1-已开票）
     */
    @Schema(description = "发票状态（0-未开票，1-已开票）")
    @ExcelProperty(value = "发票状态（0-未开票，1-已开票）")
    private String invoiceStatus;

    /**
     * 申请时间
     */
    @Schema(description = "申请时间")
    @ExcelProperty(value = "申请时间")
    private LocalDateTime applicationTime;

    /**
     * 开票时间
     */
    @Schema(description = "开票时间")
    @ExcelProperty(value = "开票时间")
    private LocalDateTime invoiceTime;

    /**
     * 邮寄时间
     */
    @Schema(description = "邮寄时间")
    @ExcelProperty(value = "邮寄时间")
    private LocalDateTime mailingTime;

    /**
     * 发票快递方式
     */
    @Schema(description = "发票快递方式")
    @ExcelProperty(value = "发票快递方式")
    private String invoiceDeliveryMethod;

    /**
     * 发票类型
     */
    @Schema(description = "发票类型")
    @ExcelProperty(value = "发票类型")
    private String invoiceType;

    /**
     * 订单开始时间
     */
    @Schema(description = "订单开始时间")
    @ExcelProperty(value = "订单开始时间")
    private LocalDateTime orderStartTime;

    /**
     * 订单结束时间
     */
    @Schema(description = "订单结束时间")
    @ExcelProperty(value = "订单结束时间")
    private LocalDateTime orderEndTime;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @ExcelProperty(value = "订单编号")
    private String billNumber;

    /**
     * 付款时间
     */
    @Schema(description = "付款时间")
    @ExcelProperty(value = "付款时间")
    private LocalDateTime paymentTime;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @ExcelProperty(value = "备注信息")
    private String remarks;
}