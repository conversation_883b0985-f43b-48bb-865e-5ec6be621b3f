/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.base.controller;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import cn.com.necloud.admin.common.controller.BaseController;
import cn.com.necloud.admin.base.model.query.CarModelsQuery;
import cn.com.necloud.admin.base.model.req.CarModelsReq;
import cn.com.necloud.admin.base.model.resp.CarModelsDetailResp;
import cn.com.necloud.admin.base.model.resp.CarModelsResp;
import cn.com.necloud.admin.base.service.CarModelsService;

/**
 * 车型列表管理 API
 *
 * <AUTHOR>
 * @since 2025/07/14 11:06
 */
@Tag(name = "车型列表管理 API")
@RestController
@CrudRequestMapping(value = "/base/carModels", api = {Api.PAGE, Api.GET, Api.CREATE, Api.UPDATE, Api.DELETE,
    Api.EXPORT})
public class CarModelsController extends BaseController<CarModelsService, CarModelsResp, CarModelsDetailResp, CarModelsQuery, CarModelsReq> {}