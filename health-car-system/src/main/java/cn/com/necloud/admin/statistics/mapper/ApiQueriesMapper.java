/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.mapper;

import cn.com.necloud.admin.statistics.model.resp.DataStatisticsResp;
import org.apache.ibatis.annotations.Param;
import top.continew.starter.data.mp.base.BaseMapper;
import cn.com.necloud.admin.statistics.model.entity.ApiQueriesDO;

import java.util.List;

/**
 * api查询结果 Mapper
 *
 * <AUTHOR>
 * @since 2025/07/21 10:40
 */
public interface ApiQueriesMapper extends BaseMapper<ApiQueriesDO> {
    List<DataStatisticsResp> selectApi(@Param("cratetime") String cratetime, @Param("endtime") String endtime);
}