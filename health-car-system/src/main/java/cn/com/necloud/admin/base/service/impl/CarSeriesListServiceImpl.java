/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.base.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.base.mapper.CarSeriesListMapper;
import cn.com.necloud.admin.base.model.entity.CarSeriesListDO;
import cn.com.necloud.admin.base.model.query.CarSeriesListQuery;
import cn.com.necloud.admin.base.model.req.CarSeriesListReq;
import cn.com.necloud.admin.base.model.resp.CarSeriesListDetailResp;
import cn.com.necloud.admin.base.model.resp.CarSeriesListResp;
import cn.com.necloud.admin.base.service.CarSeriesListService;

/**
 * 车型库车系业务实现
 *
 * <AUTHOR>
 * @since 2025/07/11 14:48
 */
@Service
@RequiredArgsConstructor
public class CarSeriesListServiceImpl extends BaseServiceImpl<CarSeriesListMapper, CarSeriesListDO, CarSeriesListResp, CarSeriesListDetailResp, CarSeriesListQuery, CarSeriesListReq> implements CarSeriesListService {}