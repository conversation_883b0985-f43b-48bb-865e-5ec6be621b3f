/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.system.mapper.user;

import org.apache.ibatis.annotations.Param;
import cn.com.necloud.admin.system.model.entity.user.UserPasswordHistoryDO;
import top.continew.starter.data.mp.base.BaseMapper;

/**
 * 用户历史密码 Mapper
 *
 * <AUTHOR>
 * @since 2024/5/16 21:58
 */
public interface UserPasswordHistoryMapper extends BaseMapper<UserPasswordHistoryDO> {

    /**
     * 删除过期历史密码
     *
     * @param userId 用户 ID
     * @param count  保留 N 个历史
     */
    void deleteExpired(@Param("userId") Long userId, @Param("count") int count);
}