/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.service;

import cn.com.necloud.admin.statistics.model.query.InterfaceQuery;
import cn.com.necloud.admin.statistics.model.resp.StatisticsApiRecordsDetailResp;

import top.continew.starter.extension.crud.model.query.PageQuery;
import cn.com.necloud.admin.statistics.model.util.PageResp;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-16
 * @Description:
 * @Version: 1.8
 */

public interface StatisticsApiService {
    /**
     * 分页查询接口统计管理
     * 
     * @param query
     * @param pageQuery
     * @return
     */
    PageResp<StatisticsApiRecordsDetailResp> page(InterfaceQuery query, PageQuery pageQuery);

}
