/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.base.model.resp;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.time.*;

/**
 * 品牌列表信息
 *
 * <AUTHOR>
 * @since 2025/07/10 20:06
 */
@Data
@Schema(description = "品牌列表信息")
public class BrandsResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * make_id: 品牌唯一标识
     */
    @Schema(description = "make_id: 品牌唯一标识")
    private Long businessId;

    /**
     * 国家唯一标识
     */
    @Schema(description = "国家唯一标识")
    private Long countryId;

    /**
     * 公司集团唯一标识
     */
    @Schema(description = "公司集团唯一标识")
    private Long companyGroupId;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    private String brandName;

    /**
     * 其他名称
     */
    @Schema(description = "其他名称")
    private String otherName;

    /**
     * 英文名称
     */
    @Schema(description = "英文名称")
    private String englishName;

    /**
     * logo_url: 品牌Logo图片地址
     */
    @Schema(description = "logo_url: 品牌Logo图片地址")
    private String brandLogoUrl;

    /**
     * logo: 品牌标识图片地址
     */
    @Schema(description = "logo: 品牌标识图片地址")
    private String brandLogoUrl2;

    /**
     * 简介信息
     */
    @Schema(description = "简介信息")
    private String introductionInfo;

    /**
     * 状态字段，标识记录当前业务状态（如：0-未开始，1-进行中，2-已完成）
     */
    @Schema(description = "状态字段，标识记录当前业务状态（如：0-未开始，1-进行中，2-已完成）")
    private Integer orderStatus;

    /**
     * 拼音简码
     */
    @Schema(description = "拼音简码")
    private String spell;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    private LocalDateTime updateTime;

    /**
     * 二手车名称
     */
    @Schema(description = "二手车名称")
    private String usedCarName;

    /**
     * 操作人姓名
     */
    @Schema(description = "操作人姓名")
    private String operatorName;

    /**
     * 是否为乘用车
     */
    @Schema(description = "是否为乘用车")
    private String isPassengerCar;
}