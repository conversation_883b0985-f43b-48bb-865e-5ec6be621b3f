/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.apicallback.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;

/**
 * 健康用车api回调接口记录实体
 *
 * <AUTHOR>
 * @since 2025/07/11 09:30
 */
@Data
@TableName("hc_api_callback_records")
public class ApiCallbackRecordsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键，唯一标识回调记录
     */
    private String businessId;

    /**
     * 状态（0-禁用，1-启用）
     */
    private String isEnabled;

    /**
     * 回调处理结果
     */
    private String callbackResult;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息描述
     */
    private String errorMessage;

    /**
     * 回调类型（1-预约回调，2-投诉回调）
     */
    private String callbackType;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 订单唯一标识
     */
    private String orderId;
}
