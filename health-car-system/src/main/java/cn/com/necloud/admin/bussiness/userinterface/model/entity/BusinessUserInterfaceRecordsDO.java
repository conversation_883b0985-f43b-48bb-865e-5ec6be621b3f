/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.userinterface.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 用户接口管理表实体
 *
 * <AUTHOR>
 * @since 2025/07/10 16:45
 */
@Data
@TableName("hc_business_user_interface_records")
public class BusinessUserInterfaceRecordsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键ID
     */
    private String businessId;

    /**
     * 客户唯一标识
     */
    private String customerId;

    /**
     * API名称
     */
    private String apiName;

    /**
     * state: 用户状态（0-禁用，1-启用）
     */
    private String userStatus;

    /**
     * 限制次数
     */
    private String limitCount;

    /**
     * 活动参与次数
     */
    private BigDecimal activityParticipationCount;

    /**
     * 价格，单位：元
     */
    private BigDecimal price;
}
