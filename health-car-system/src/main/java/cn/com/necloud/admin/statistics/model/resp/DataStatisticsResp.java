/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-20
 * @Description:
 * @Version: 1.8
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "数据源响应结果")
public class DataStatisticsResp implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "供应商名称")
    private String suppliername;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "备注")
    private String remarks;
    @Schema(description = "接口名称")
    private String apiname;
    @Schema(description = "查询数量")
    private BigDecimal total;
    @Schema(description = "价格")
    private BigDecimal price;
}
