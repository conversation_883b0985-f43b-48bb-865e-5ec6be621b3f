/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.consumer.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.consumer.mapper.ConsumerUserMapper;
import cn.com.necloud.admin.consumer.model.entity.ConsumerUserDO;
import cn.com.necloud.admin.consumer.model.query.ConsumerUserQuery;
import cn.com.necloud.admin.consumer.model.req.ConsumerUserReq;
import cn.com.necloud.admin.consumer.model.resp.ConsumerUserDetailResp;
import cn.com.necloud.admin.consumer.model.resp.ConsumerUserResp;
import cn.com.necloud.admin.consumer.service.ConsumerUserService;

/**
 * 消费用户表业务实现
 *
 * <AUTHOR>
 * @since 2025/07/16 14:42
 */
@Service
@RequiredArgsConstructor
public class ConsumerUserServiceImpl extends BaseServiceImpl<ConsumerUserMapper, ConsumerUserDO, ConsumerUserResp, ConsumerUserDetailResp, ConsumerUserQuery, ConsumerUserReq> implements ConsumerUserService {}