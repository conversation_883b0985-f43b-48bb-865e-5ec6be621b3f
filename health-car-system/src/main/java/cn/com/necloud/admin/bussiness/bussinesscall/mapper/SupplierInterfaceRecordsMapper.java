/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.bussinesscall.mapper;

import cn.com.necloud.admin.statistics.model.resp.DataStatisticsResp;
import org.apache.ibatis.annotations.Param;
import top.continew.starter.data.mp.base.BaseMapper;
import cn.com.necloud.admin.bussiness.bussinesscall.model.entity.SupplierInterfaceRecordsDO;

import java.util.List;

/**
 * 厂商接口调用记录表 Mapper
 *
 * <AUTHOR>
 * @since 2025/07/11 14:36
 */
public interface SupplierInterfaceRecordsMapper extends BaseMapper<SupplierInterfaceRecordsDO> {
    /*
     * @return: 数据源基础信息查询
     * @Author:  xisheng
     * @date:  2025/7/21 09:53
     */

    List<DataStatisticsResp> selectDataSourceStatistics(@Param("starttime") String starttime,
                                                        @Param("endtime") String endtime);

}