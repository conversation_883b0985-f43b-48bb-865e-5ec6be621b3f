/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.system.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import cn.com.necloud.admin.common.constant.UiConstants;
import top.continew.starter.core.enums.BaseEnum;

/**
 * 消息类型枚举
 *
 * <AUTHOR>
 * @since 2023/11/2 20:08
 */
@Getter
@RequiredArgsConstructor
public enum MessageTypeEnum implements BaseEnum<Integer> {

    /**
     * 系统消息
     */
    SYSTEM(1, "系统消息", UiConstants.COLOR_PRIMARY),

    /**
     * 安全消息
     */
    SECURITY(2, "安全消息", UiConstants.COLOR_WARNING),;

    private final Integer value;
    private final String description;
    private final String color;
}
