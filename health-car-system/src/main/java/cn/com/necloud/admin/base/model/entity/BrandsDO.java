/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.base.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;

/**
 * 品牌列表实体
 *
 * <AUTHOR>
 * @since 2025/07/11 11:06
 */
@Data
@TableName("hc_brands")
public class BrandsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * make_id: 品牌唯一标识
     */
    private Long businessId;

    /**
     * 国家唯一标识
     */
    private Long countryId;

    /**
     * 公司集团唯一标识
     */
    private Long companyGroupId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 其他名称
     */
    private String otherName;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * logo_url: 品牌Logo图片地址
     */
    private String brandLogoUrl;

    /**
     * 品牌标识
     */
    private String brandLogo;

    /**
     * 简介信息
     */
    private String introduction;

    /**
     * 状态（0-禁用，1-启用）
     */
    private Integer isStatus;

    /**
     * 拼音简码
     */
    private String spell;

    /**
     * 二手车名称
     */
    private String usedCarName;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 是否为乘用车
     */
    private String isPassengerCar;
}
