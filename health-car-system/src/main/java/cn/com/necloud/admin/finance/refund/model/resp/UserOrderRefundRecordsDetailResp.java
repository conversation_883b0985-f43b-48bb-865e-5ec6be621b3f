/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.refund.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import cn.com.necloud.admin.common.model.resp.NewBaseDetailResp;
import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 用户订单退款记录详情信息
 *
 * <AUTHOR>
 * @since 2025/07/16 11:49
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "用户订单退款记录详情信息")
public class UserOrderRefundRecordsDetailResp extends NewBaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户退款记录主键ID
     */
    @Schema(description = "用户退款记录主键ID")
    @ExcelProperty(value = "用户退款记录主键ID")
    private String businessId;

    /**
     * 客户唯一标识
     */
    @Schema(description = "客户唯一标识")
    @ExcelProperty(value = "客户唯一标识")
    private String customerId;

    /**
     * 退款金额，单位：元
     */
    @Schema(description = "退款金额，单位：元")
    @ExcelProperty(value = "退款金额，单位：元")
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    @Schema(description = "退款时间")
    @ExcelProperty(value = "退款时间")
    private LocalDateTime refundTime;

    /**
     * 退款记录备注信息
     */
    @Schema(description = "退款记录备注信息")
    @ExcelProperty(value = "退款记录备注信息")
    private String refundRemark;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @ExcelProperty(value = "订单编号")
    private String orderNumber;
}