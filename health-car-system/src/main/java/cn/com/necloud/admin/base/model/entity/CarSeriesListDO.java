/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.base.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;

/**
 * 车型库车系实体
 *
 * <AUTHOR>
 * @since 2025/07/11 14:48
 */
@Data
@TableName("hc_car_series_list")
public class CarSeriesListDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 子模型主键
     */
    private Long subModelId;

    /**
     * 子模型名称
     */
    private String subModelName;

    /**
     * 其他名称
     */
    private String otherName;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * 网站地址
     */
    private String websiteUrl;

    /**
     * 子模型介绍
     */
    private String subModelIntroduction;

    /**
     * 拼音缩写
     */
    private String pinyinAbbreviation;

    /**
     * 全拼名称
     */
    private String fullPinyinName;

    /**
     * 子模型显示名称
     */
    private String subModelDisplayName;

    /**
     * 优势描述
     */
    private String advantageDescription;

    /**
     * 子模型状态（0-禁用，1-启用）
     */
    private Integer subModelStatus;

    /**
     * 缺陷描述
     */
    private String defectDescription;

    /**
     * 模型图片地址
     */
    private String modelImageUrl;

    /**
     * 车辆热度值
     */
    private Integer vehiclePopularityScore;

    /**
     * 厂商唯一标识
     */
    private Long manufacturerId;

    /**
     * 模型等级ID
     */
    private Long modelLevelId;

    /**
     * 通用等级
     */
    private Long generalLevel;

    /**
     * 模型等级
     */
    private Long modelLevel;

    /**
     * 厂商ID，关联t_make表
     */
    private Integer makeId;
}
