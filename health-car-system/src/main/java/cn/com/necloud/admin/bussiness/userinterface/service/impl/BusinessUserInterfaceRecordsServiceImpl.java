/*
 * Copyright (c) 2022-present <PERSON><PERSON>c <PERSON>s. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.userinterface.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.bussiness.userinterface.mapper.BusinessUserInterfaceRecordsMapper;
import cn.com.necloud.admin.bussiness.userinterface.model.entity.BusinessUserInterfaceRecordsDO;
import cn.com.necloud.admin.bussiness.userinterface.model.query.BusinessUserInterfaceRecordsQuery;
import cn.com.necloud.admin.bussiness.userinterface.model.req.BusinessUserInterfaceRecordsReq;
import cn.com.necloud.admin.bussiness.userinterface.model.resp.BusinessUserInterfaceRecordsDetailResp;
import cn.com.necloud.admin.bussiness.userinterface.model.resp.BusinessUserInterfaceRecordsResp;
import cn.com.necloud.admin.bussiness.userinterface.service.BusinessUserInterfaceRecordsService;

/**
 * 用户接口管理表业务实现
 *
 * <AUTHOR>
 * @since 2025/07/10 16:45
 */
@Service
@RequiredArgsConstructor
public class BusinessUserInterfaceRecordsServiceImpl extends BaseServiceImpl<BusinessUserInterfaceRecordsMapper, BusinessUserInterfaceRecordsDO, BusinessUserInterfaceRecordsResp, BusinessUserInterfaceRecordsDetailResp, BusinessUserInterfaceRecordsQuery, BusinessUserInterfaceRecordsReq> implements BusinessUserInterfaceRecordsService {}