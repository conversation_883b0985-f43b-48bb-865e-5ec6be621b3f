/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.productconfig.service.impl;

import cn.com.necloud.admin.bussiness.productconfig.model.req.ProductConfigsImportReq;
import cn.com.necloud.admin.bussiness.productconfig.model.req.ProductConfigsImportRowReq;
import cn.com.necloud.admin.bussiness.productconfig.model.resp.ProductConfigsImportParseResp;
import cn.com.necloud.admin.bussiness.productconfig.model.resp.ProductConfigsImportResp;
import cn.com.necloud.admin.common.constant.CacheConstants;
import cn.com.necloud.admin.system.enums.ImportPolicyEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import top.continew.starter.cache.redisson.util.RedisUtils;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.util.FileUploadUtils;
import top.continew.starter.core.validation.CheckUtils;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.bussiness.productconfig.mapper.ProductConfigsMapper;
import cn.com.necloud.admin.bussiness.productconfig.model.entity.ProductConfigsDO;
import cn.com.necloud.admin.bussiness.productconfig.model.query.ProductConfigsQuery;
import cn.com.necloud.admin.bussiness.productconfig.model.req.ProductConfigsReq;
import cn.com.necloud.admin.bussiness.productconfig.model.resp.ProductConfigsDetailResp;
import cn.com.necloud.admin.bussiness.productconfig.model.resp.ProductConfigsResp;
import cn.com.necloud.admin.bussiness.productconfig.service.ProductConfigsService;

import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 产品信息管理业务实现
 *
 * <AUTHOR>
 * @since 2025/07/11 15:01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductConfigsServiceImpl extends BaseServiceImpl<ProductConfigsMapper, ProductConfigsDO, ProductConfigsResp, ProductConfigsDetailResp, ProductConfigsQuery, ProductConfigsReq> implements ProductConfigsService {

    @Override
    public void downloadImportTemplate(HttpServletResponse response) throws IOException {
        try {
            FileUploadUtils.download(response, ResourceUtil
                .getStream("templates/import/product_configs.xlsx"), "产品信息导入模板.xlsx");
        } catch (Exception e) {
            log.error("下载产品信息导入模板失败：{}", e.getMessage(), e);
            throw new BusinessException("下载产品信息导入模板失败");
        }
    }

    @Override
    public ProductConfigsImportParseResp parseImport(MultipartFile file) {
        ProductConfigsImportParseResp importParseResp = new ProductConfigsImportParseResp();
        List<ProductConfigsImportRowReq> importRowList;

        // 读取表格数据
        try {
            importRowList = EasyExcel.read(file.getInputStream())
                .head(ProductConfigsImportRowReq.class)
                .sheet()
                .headRowNumber(1)
                .doReadSync();
        } catch (Exception e) {
            log.error("产品信息导入数据文件解析异常：{}", e.getMessage(), e);
            throw new BusinessException("数据文件解析异常");
        }

        // 总计行数
        importParseResp.setTotalRows(importRowList.size());
        CheckUtils.throwIfEmpty(importRowList, "数据文件格式不正确");

        // 有效行数：过滤无效数据
        List<ProductConfigsImportRowReq> validRowList = this.filterImportData(importRowList);
        importParseResp.setValidRows(validRowList.size());
        CheckUtils.throwIfEmpty(validRowList, "数据文件格式不正确");

        // 查询重复API名称
        importParseResp
            .setDuplicateApiNameRows(countExistByField(validRowList, ProductConfigsImportRowReq::getApiName, false));

        // 设置导入会话并缓存数据，有效期10分钟
        String importKey = IdUtil.fastSimpleUUID();
        RedisUtils.set(CacheConstants.DATA_IMPORT_KEY + importKey, JSONUtil.toJsonStr(validRowList), Duration
            .ofMinutes(10));
        importParseResp.setImportKey(importKey);

        return importParseResp;
    }

    @Override
    public ProductConfigsImportResp importData(ProductConfigsImportReq importReq) {
        // 从缓存中获取导入数据
        String cacheData = RedisUtils.get(CacheConstants.DATA_IMPORT_KEY + importReq.getImportKey());
        CheckUtils.throwIfBlank(cacheData, "导入已过期，请重新上传");

        List<ProductConfigsImportRowReq> importRowList = JSONUtil.toList(cacheData, ProductConfigsImportRowReq.class);
        CheckUtils.throwIfEmpty(importRowList, "导入数据为空");

        ProductConfigsImportResp importResp = new ProductConfigsImportResp();
        importResp.setTotalRows(importRowList.size());

        int insertRows = 0;
        int updateRows = 0;
        int skipRows = 0;

        for (ProductConfigsImportRowReq rowReq : importRowList) {
            // 检查是否存在相同API名称的记录
            ProductConfigsDO existingRecord = baseMapper.selectOne(new LambdaQueryWrapper<ProductConfigsDO>()
                .eq(ProductConfigsDO::getApiName, rowReq.getApiName()));

            if (existingRecord != null) {
                // 处理重复数据
                if (ImportPolicyEnum.SKIP.equals(importReq.getDuplicateApiName())) {
                    skipRows++;
                    continue;
                } else if (ImportPolicyEnum.UPDATE.equals(importReq.getDuplicateApiName())) {
                    // 更新现有记录
                    ProductConfigsDO updateRecord = BeanUtil.copyProperties(rowReq, ProductConfigsDO.class);
                    updateRecord.setId(existingRecord.getId());
                    updateRecord.setBusinessId(existingRecord.getBusinessId()); // 保持原有的businessId
                    baseMapper.updateById(updateRecord);
                    updateRows++;
                }
            } else {
                // 新增记录，自动生成businessId
                ProductConfigsDO newRecord = BeanUtil.copyProperties(rowReq, ProductConfigsDO.class);
                // 生成UUID并去除-字符
                newRecord.setBusinessId(IdUtil.fastSimpleUUID());
                baseMapper.insert(newRecord);
                insertRows++;
            }
        }

        importResp.setInsertRows(insertRows);
        importResp.setUpdateRows(updateRows);
        importResp.setSkipRows(skipRows);

        // 清除缓存
        RedisUtils.delete(CacheConstants.DATA_IMPORT_KEY + importReq.getImportKey());

        return importResp;
    }

    /**
     * 过滤导入数据，移除无效行
     *
     * @param importRowList 导入行数据列表
     * @return 有效行数据列表
     */
    private List<ProductConfigsImportRowReq> filterImportData(List<ProductConfigsImportRowReq> importRowList) {
        return importRowList.stream()
            .filter(row -> CharSequenceUtil.isNotBlank(row.getApiName()) && CharSequenceUtil.isNotBlank(row
                .getProductType()) && row.getUnitPrice() != null && CharSequenceUtil.isNotBlank(row.getIsReported()))
            .collect(Collectors.toList());
    }

    /**
     * 统计指定字段存在的行数
     *
     * @param importRowList     导入行数据列表
     * @param importFieldGetter 导入数据字段获取器
     * @param allowBlank        是否允许空值
     * @return 存在的行数
     */
    private int countExistByField(List<ProductConfigsImportRowReq> importRowList,
                                  Function<ProductConfigsImportRowReq, String> importFieldGetter,
                                  boolean allowBlank) {
        List<String> fieldValues = importRowList.stream()
            .map(importFieldGetter)
            .filter(value -> allowBlank || CharSequenceUtil.isNotBlank(value))
            .distinct()
            .collect(Collectors.toList());

        if (CollUtil.isEmpty(fieldValues)) {
            return 0;
        }

        return Math.toIntExact(baseMapper.selectCount(new LambdaQueryWrapper<ProductConfigsDO>()
            .in(ProductConfigsDO::getApiName, fieldValues)));
    }
}