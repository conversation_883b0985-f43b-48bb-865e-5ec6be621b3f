/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.pusherrrecord.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import java.io.Serial;
import java.time.*;

/**
 * 健康用车api推送错误记录表信息
 *
 * <AUTHOR>
 * @since 2025/07/11 11:09
 */
@Data
@Schema(description = "健康用车api推送错误记录表信息")
public class ApiPushErrorLogResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键ID
     */
    @Schema(description = "业务主键ID")
    private String businessId;

    /**
     * 更新人名称
     */
    @Schema(description = "更新人名称")
    private String updateUser;

    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNumber;

    /**
     * 推送内容详情
     */
    @Schema(description = "推送内容详情")
    private String pushContent;

    /**
     * 请求地址
     */
    @Schema(description = "请求地址")
    private String requestUrl;

    /**
     * 重新推送结果
     */
    @Schema(description = "重新推送结果")
    private String repushResult;

    /**
     * 错误结果描述
     */
    @Schema(description = "错误结果描述")
    private String errorResultDescription;

    /**
     * 下次推送时间
     */
    @Schema(description = "下次推送时间")
    private LocalDateTime nextPushTime;

    /**
     * 下次推送标志
     */
    @Schema(description = "下次推送标志")
    private String nextPushFlag;
}