/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.datacache.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import java.io.Serial;
import java.time.*;

/**
 * 汽车数据缓存表信息
 *
 * <AUTHOR>
 * @since 2025/07/11 09:43
 */
@Data
@Schema(description = "汽车数据缓存表信息")
public class CarDataCacheRecordsResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String businessId;

    /**
     * 更新人名称
     */
    @Schema(description = "更新人名称")
    private String updateUser;

    /**
     * 数据更新时间
     */
    @Schema(description = "数据更新时间")
    private LocalDateTime updateTime;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNumber;

    /**
     * 客户唯一标识
     */
    @Schema(description = "客户唯一标识")
    private String customerId;

    /**
     * 车辆识别码
     */
    @Schema(description = "车辆识别码")
    private String vehicleIdentificationNumber;

    /**
     * 查询类型
     */
    @Schema(description = "查询类型")
    private String queryType;

    /**
     * 数据内容
     */
    @Schema(description = "数据内容")
    private String dataContent;

    /**
     * 数据类型标识
     */
    @Schema(description = "数据类型标识")
    private String dataTypeIdentifier;
}