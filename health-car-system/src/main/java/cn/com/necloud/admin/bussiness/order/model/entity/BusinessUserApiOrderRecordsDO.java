/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.order.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 商业用户 api 调用订单表实体
 *
 * <AUTHOR>
 * @since 2025/07/10 19:06
 */
@Data
@TableName("hc_business_user_api_order_records")
public class BusinessUserApiOrderRecordsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键ID
     */
    private String businessId;

    /**
     * 车辆识别码
     */
    private String vin;

    /**
     * 车辆类型
     */
    private String vehicleType;

    /**
     * 营业执照号码
     */
    private String businessLicenseNumber;

    /**
     * 客户唯一标识
     */
    private String customerId;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 状态（0-待处理，1-处理中，2-已完成）
     */
    private String orderStatus;

    /**
     * 车辆识别代码2
     */
    private String vehicleIdentificationNumber;

    /**
     * 订单商品数量
     */
    private Integer orderItemCount;

    /**
     * sendtime: 消息发送时间
     */
    private LocalDateTime messageSendTime;

    /**
     * 调用是否成功标识（0-失败，1-成功）
     */
    private String isSuccess;

    /**
     * 用途分析原因描述
     */
    private String usageAnalysisReason;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 所属区域
     */
    private String areaName;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 超时时间
     */
    private String timeoutDuration;

    /**
     * 参数信息（JSON格式存储）
     */
    private String parameters;

    /**
     * API响应内容
     */
    private String apiResponse;

    /**
     * 召回响应内容
     */
    private String recallResponseContent;

    /**
     * 请求地址
     */
    private String url;

    /**
     * 订单业务ID3
     */
    private String orderBusinessId;

    /**
     * 状态（0-禁用，1-启用）
     */
    private String isEnabled;

    /**
     * 流程步骤名称
     */
    private String processStepName;

    /**
     * 推送内容
     */
    private String pushContent;

    /**
     * 推送响应结果
     */
    private String pushResponse;

    /**
     * 召回内容
     */
    private String recallContent;

    /**
     * 参数信息
     */
    private String parameterInfo;

    /**
     * API名称
     */
    private String apiName;

    /**
     * 数据类型标识
     */
    private String dataTypeIdentifier;

    /**
     * 缓存订单编号
     */
    private String cacheOrderNumber;

    /**
     * 报告地址
     */
    private String reportUrl;

    /**
     * 健康档案订单数据
     */
    private String healthRecordOrderData;

    /**
     * 钱包余额，单位：元
     */
    private BigDecimal walletBalance;

    /**
     * 商品单价，单位：元
     */
    private BigDecimal unitPrice;

    /**
     * 消费金额，单位：元
     */
    private BigDecimal consumptionAmount;

    /**
     * 响应时间
     */
    private LocalDateTime responseTime;

    /**
     * 召回时间
     */
    private LocalDateTime recallTime;

    /**
     * 合并数据内容
     */
    private String mergedDataContent;

    /**
     * 回调超时时间
     */
    private String callbackTimeout;

    /**
     * 召回编号
     */
    private String recallNumber;

    /**
     * 召回成功标识
     */
    private String recallSuccessFlag;

    /**
     * 健康档案成功标识
     */
    private String healthRecordSuccessFlag;

    /**
     * 查询成功标识
     */
    private String querySuccessFlag;

    /**
     * 成本价，单位：元
     */
    private BigDecimal costPrice;

    /**
     * 策略名称或编码
     */
    private String strategyNameOrCode;
}
