/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.brandprice.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.finance.brandprice.mapper.BrandPriceConfigMapper;
import cn.com.necloud.admin.finance.brandprice.model.entity.BrandPriceConfigDO;
import cn.com.necloud.admin.finance.brandprice.model.query.BrandPriceConfigQuery;
import cn.com.necloud.admin.finance.brandprice.model.req.BrandPriceConfigReq;
import cn.com.necloud.admin.finance.brandprice.model.resp.BrandPriceConfigDetailResp;
import cn.com.necloud.admin.finance.brandprice.model.resp.BrandPriceConfigResp;
import cn.com.necloud.admin.finance.brandprice.service.BrandPriceConfigService;

/**
 * 品牌金额配置表业务实现
 *
 * <AUTHOR>
 * @since 2025/07/15 17:56
 */
@Service
@RequiredArgsConstructor
public class BrandPriceConfigServiceImpl extends BaseServiceImpl<BrandPriceConfigMapper, BrandPriceConfigDO, BrandPriceConfigResp, BrandPriceConfigDetailResp, BrandPriceConfigQuery, BrandPriceConfigReq> implements BrandPriceConfigService {}