/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.order.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import cn.com.necloud.admin.common.model.resp.NewBaseDetailResp;
import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 商业用户 api 调用订单表详情信息
 *
 * <AUTHOR>
 * @since 2025/07/10 19:06
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "商业用户 api 调用订单表详情信息")
public class BusinessUserApiOrderRecordsDetailResp extends NewBaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键ID
     */
    @Schema(description = "业务主键ID")
    @ExcelProperty(value = "业务主键ID")
    private String businessId;

    /**
     * 车辆识别码
     */
    @Schema(description = "车辆识别码")
    @ExcelProperty(value = "车辆识别码")
    private String vin;

    /**
     * 车辆类型
     */
    @Schema(description = "车辆类型")
    @ExcelProperty(value = "车辆类型")
    private String vehicleType;

    /**
     * 营业执照号码
     */
    @Schema(description = "营业执照号码")
    @ExcelProperty(value = "营业执照号码")
    private String businessLicenseNumber;

    /**
     * 客户唯一标识
     */
    @Schema(description = "客户唯一标识")
    @ExcelProperty(value = "客户唯一标识")
    private String customerId;

    /**
     * IP地址
     */
    @Schema(description = "IP地址")
    @ExcelProperty(value = "IP地址")
    private String ipAddress;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @ExcelProperty(value = "订单编号")
    private String orderNumber;

    /**
     * 状态（0-待处理，1-处理中，2-已完成）
     */
    @Schema(description = "状态（0-待处理，1-处理中，2-已完成）")
    @ExcelProperty(value = "状态（0-待处理，1-处理中，2-已完成）")
    private String orderStatus;

    /**
     * 车辆识别代码2
     */
    @Schema(description = "车辆识别代码2")
    @ExcelProperty(value = "车辆识别代码2")
    private String vehicleIdentificationNumber;

    /**
     * 订单商品数量
     */
    @Schema(description = "订单商品数量")
    @ExcelProperty(value = "订单商品数量")
    private Integer orderItemCount;

    /**
     * sendtime: 消息发送时间
     */
    @Schema(description = "sendtime: 消息发送时间")
    @ExcelProperty(value = "sendtime: 消息发送时间")
    private LocalDateTime messageSendTime;

    /**
     * 调用是否成功标识（0-失败，1-成功）
     */
    @Schema(description = "调用是否成功标识（0-失败，1-成功）")
    @ExcelProperty(value = "调用是否成功标识（0-失败，1-成功）")
    private String isSuccess;

    /**
     * 用途分析原因描述
     */
    @Schema(description = "用途分析原因描述")
    @ExcelProperty(value = "用途分析原因描述")
    private String usageAnalysisReason;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @ExcelProperty(value = "备注信息")
    private String remarks;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @ExcelProperty(value = "数量")
    private Integer quantity;

    /**
     * 所属区域
     */
    @Schema(description = "所属区域")
    @ExcelProperty(value = "所属区域")
    private String areaName;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    @ExcelProperty(value = "业务类型")
    private String businessType;

    /**
     * 超时时间
     */
    @Schema(description = "超时时间")
    @ExcelProperty(value = "超时时间")
    private String timeoutDuration;

    /**
     * 参数信息（JSON格式存储）
     */
    @Schema(description = "参数信息（JSON格式存储）")
    @ExcelProperty(value = "参数信息（JSON格式存储）")
    private String parameters;

    /**
     * API响应内容
     */
    @Schema(description = "API响应内容")
    @ExcelProperty(value = "API响应内容")
    private String apiResponse;

    /**
     * 召回响应内容
     */
    @Schema(description = "召回响应内容")
    @ExcelProperty(value = "召回响应内容")
    private String recallResponseContent;

    /**
     * 请求地址
     */
    @Schema(description = "请求地址")
    @ExcelProperty(value = "请求地址")
    private String url;

    /**
     * 订单业务ID3
     */
    @Schema(description = "订单业务ID3")
    @ExcelProperty(value = "订单业务ID3")
    private String orderBusinessId;

    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态（0-禁用，1-启用）")
    @ExcelProperty(value = "状态（0-禁用，1-启用）")
    private String isEnabled;

    /**
     * 流程步骤名称
     */
    @Schema(description = "流程步骤名称")
    @ExcelProperty(value = "流程步骤名称")
    private String processStepName;

    /**
     * 推送内容
     */
    @Schema(description = "推送内容")
    @ExcelProperty(value = "推送内容")
    private String pushContent;

    /**
     * 推送响应结果
     */
    @Schema(description = "推送响应结果")
    @ExcelProperty(value = "推送响应结果")
    private String pushResponse;

    /**
     * 召回内容
     */
    @Schema(description = "召回内容")
    @ExcelProperty(value = "召回内容")
    private String recallContent;

    /**
     * 参数信息
     */
    @Schema(description = "参数信息")
    @ExcelProperty(value = "参数信息")
    private String parameterInfo;

    /**
     * API名称
     */
    @Schema(description = "API名称")
    @ExcelProperty(value = "API名称")
    private String apiName;

    /**
     * 数据类型标识
     */
    @Schema(description = "数据类型标识")
    @ExcelProperty(value = "数据类型标识")
    private String dataTypeIdentifier;

    /**
     * 缓存订单编号
     */
    @Schema(description = "缓存订单编号")
    @ExcelProperty(value = "缓存订单编号")
    private String cacheOrderNumber;

    /**
     * 报告地址
     */
    @Schema(description = "报告地址")
    @ExcelProperty(value = "报告地址")
    private String reportUrl;

    /**
     * 健康档案订单数据
     */
    @Schema(description = "健康档案订单数据")
    @ExcelProperty(value = "健康档案订单数据")
    private String healthRecordOrderData;

    /**
     * 钱包余额，单位：元
     */
    @Schema(description = "钱包余额，单位：元")
    @ExcelProperty(value = "钱包余额，单位：元")
    private BigDecimal walletBalance;

    /**
     * 商品单价，单位：元
     */
    @Schema(description = "商品单价，单位：元")
    @ExcelProperty(value = "商品单价，单位：元")
    private BigDecimal unitPrice;

    /**
     * 消费金额，单位：元
     */
    @Schema(description = "消费金额，单位：元")
    @ExcelProperty(value = "消费金额，单位：元")
    private BigDecimal consumptionAmount;

    /**
     * 响应时间
     */
    @Schema(description = "响应时间")
    @ExcelProperty(value = "响应时间")
    private LocalDateTime responseTime;

    /**
     * 召回时间
     */
    @Schema(description = "召回时间")
    @ExcelProperty(value = "召回时间")
    private LocalDateTime recallTime;

    /**
     * 合并数据内容
     */
    @Schema(description = "合并数据内容")
    @ExcelProperty(value = "合并数据内容")
    private String mergedDataContent;

    /**
     * 回调超时时间
     */
    @Schema(description = "回调超时时间")
    @ExcelProperty(value = "回调超时时间")
    private String callbackTimeout;

    /**
     * 召回编号
     */
    @Schema(description = "召回编号")
    @ExcelProperty(value = "召回编号")
    private String recallNumber;

    /**
     * 召回成功标识
     */
    @Schema(description = "召回成功标识")
    @ExcelProperty(value = "召回成功标识")
    private String recallSuccessFlag;

    /**
     * 健康档案成功标识
     */
    @Schema(description = "健康档案成功标识")
    @ExcelProperty(value = "健康档案成功标识")
    private String healthRecordSuccessFlag;

    /**
     * 查询成功标识
     */
    @Schema(description = "查询成功标识")
    @ExcelProperty(value = "查询成功标识")
    private String querySuccessFlag;

    /**
     * 成本价，单位：元
     */
    @Schema(description = "成本价，单位：元")
    @ExcelProperty(value = "成本价，单位：元")
    private BigDecimal costPrice;

    /**
     * 策略名称或编码
     */
    @Schema(description = "策略名称或编码")
    @ExcelProperty(value = "策略名称或编码")
    private String strategyNameOrCode;
}