/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;

/**
 * api查询结果实体
 *
 * <AUTHOR>
 * @since 2025/07/21 10:40
 */
@Data
@TableName("hc_api_queries")
public class ApiQueriesDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * API查询记录唯一标识
     */
    private String businessId;

    /**
     * API查询名称
     */
    private String apiQueryName;

    /**
     * 接口响应结果
     */
    private String responseResult;

    /**
     * API请求参数
     */
    private String apiRequestParameters;

    /**
     * API查询条件排序规则
     */
    private String sortRule;
}
