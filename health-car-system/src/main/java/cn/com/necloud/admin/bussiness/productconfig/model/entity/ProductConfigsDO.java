/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.productconfig.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 产品信息管理实体
 *
 * <AUTHOR>
 * @since 2025/07/11 15:01
 */
@Data
@TableName("hc_product_configs")
public class ProductConfigsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键，jkyc产品配置唯一标识
     */
    private String businessId;

    /**
     * API名称
     */
    private String apiName;

    /**
     * 备注信息
     */
    private String remarkInfo;

    /**
     * 产品类型标识
     */
    private String productType;

    /**
     * 商品价格，单位：元
     */
    private BigDecimal unitPrice;

    /**
     * 是否上报标志
     */
    private String isReported;
}
