/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-16
 * @Description:
 * @Version: 1.8
 */
@Data
@Schema(description = "收支出相关参数传递")
public class RevenueExpensesQuery {
    @Serial
    private static final long serialVersionUID = 1L;

    //通道是否是1/2
    /* 1通道 2通道
     */
    @Schema(description = "通道选择", example = "通道选择")
    private String userChannel;

    //开始时间
    @Schema(description = "开始时间", example = "开始时间")
    private String cratetime;
    //结束时间
    @Schema(description = "结束时间", example = "结束时间")
    private String endtime;
    //用户
    @Schema(description = "用户名称", example = "用户名称")
    private String customerId;

}
