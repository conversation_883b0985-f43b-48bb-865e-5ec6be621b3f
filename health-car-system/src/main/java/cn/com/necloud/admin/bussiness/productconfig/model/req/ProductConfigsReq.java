/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.productconfig.model.req;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.math.BigDecimal;

/**
 * 产品信息管理创建或修改参数
 *
 * <AUTHOR>
 * @since 2025/07/11 15:01
 */
@Data
@Schema(description = "产品信息管理创建或修改参数")
public class ProductConfigsReq implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键，jkyc产品配置唯一标识
     */
    @Schema(description = "业务主键，jkyc产品配置唯一标识", example = "JKYC_001")
    @NotBlank(message = "业务主键不能为空")
    @Length(max = 100, message = "业务主键长度不能超过 {max} 个字符")
    private String businessId;

    /**
     * API名称
     */
    @Schema(description = "API名称", example = "健康体检API")
    @NotBlank(message = "API名称不能为空")
    @Length(max = 200, message = "API名称长度不能超过 {max} 个字符")
    private String apiName;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息", example = "用于健康体检数据查询")
    @Length(max = 500, message = "备注信息长度不能超过 {max} 个字符")
    private String remarkInfo;

    /**
     * 产品类型标识
     */
    @Schema(description = "产品类型标识", example = "HEALTH_CHECK")
    @NotBlank(message = "产品类型标识不能为空")
    @Length(max = 50, message = "产品类型标识长度不能超过 {max} 个字符")
    private String productType;

    /**
     * 商品价格，单位：元
     */
    @Schema(description = "商品价格，单位：元", example = "10.50")
    @NotNull(message = "商品价格不能为空")
    @DecimalMin(value = "0.01", message = "商品价格必须大于0")
    private BigDecimal unitPrice;

    /**
     * 是否上报标志
     */
    @Schema(description = "是否上报标志", example = "Y")
    @NotBlank(message = "是否上报标志不能为空")
    @Length(max = 1, message = "是否上报标志长度不能超过 {max} 个字符")
    private String isReported;
}