/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.apicallback.controller;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import cn.com.necloud.admin.common.controller.BaseController;
import cn.com.necloud.admin.bussiness.apicallback.model.query.ApiCallbackRecordsQuery;
import cn.com.necloud.admin.bussiness.apicallback.model.req.ApiCallbackRecordsReq;
import cn.com.necloud.admin.bussiness.apicallback.model.resp.ApiCallbackRecordsDetailResp;
import cn.com.necloud.admin.bussiness.apicallback.model.resp.ApiCallbackRecordsResp;
import cn.com.necloud.admin.bussiness.apicallback.service.ApiCallbackRecordsService;

/**
 * 健康用车api回调接口记录管理 API
 *
 * <AUTHOR>
 * @since 2025/07/11 09:30
 */
@Tag(name = "健康用车api回调接口记录管理 API")
@RestController
@CrudRequestMapping(value = "/apicallback/apiCallbackRecords", api = {Api.PAGE, Api.GET, Api.CREATE, Api.UPDATE,
    Api.DELETE, Api.EXPORT})
public class ApiCallbackRecordsController extends BaseController<ApiCallbackRecordsService, ApiCallbackRecordsResp, ApiCallbackRecordsDetailResp, ApiCallbackRecordsQuery, ApiCallbackRecordsReq> {}