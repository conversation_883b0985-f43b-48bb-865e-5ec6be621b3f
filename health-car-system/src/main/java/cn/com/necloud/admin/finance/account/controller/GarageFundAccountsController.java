/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.account.controller;

import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import cn.com.necloud.admin.common.controller.BaseController;
import cn.com.necloud.admin.finance.account.model.query.GarageFundAccountsQuery;
import cn.com.necloud.admin.finance.account.model.req.GarageFundAccountsReq;
import cn.com.necloud.admin.finance.account.model.resp.GarageFundAccountsDetailResp;
import cn.com.necloud.admin.finance.account.model.resp.GarageFundAccountsResp;
import cn.com.necloud.admin.finance.account.service.GarageFundAccountsService;

/**
 * 汽修资金账户表管理 API
 *
 * <AUTHOR>
 * @since 2025/07/15 15:55
 */
@Tag(name = "汽修资金账户表管理 API")
@RestController
@CrudRequestMapping(value = "/account/garageFundAccounts", api = {Api.PAGE, Api.GET, Api.CREATE, Api.UPDATE, Api.DELETE,
    Api.EXPORT})
public class GarageFundAccountsController extends BaseController<GarageFundAccountsService, GarageFundAccountsResp, GarageFundAccountsDetailResp, GarageFundAccountsQuery, GarageFundAccountsReq> {}