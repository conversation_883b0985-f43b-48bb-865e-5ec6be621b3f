/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.consumer.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import cn.com.necloud.admin.common.model.resp.NewBaseDetailResp;
import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 消费用户表详情信息
 *
 * <AUTHOR>
 * @since 2025/07/16 14:42
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "消费用户表详情信息")
public class ConsumerUserDetailResp extends NewBaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户业务主键
     */
    @Schema(description = "用户业务主键")
    @ExcelProperty(value = "用户业务主键")
    private String businessId;

    /**
     * 用户信息创建时间
     */
    @Schema(description = "用户信息创建时间")
    @ExcelProperty(value = "用户信息创建时间")
    private LocalDateTime userCreateTime;

    /**
     * 用户信息更新时间
     */
    @Schema(description = "用户信息更新时间")
    @ExcelProperty(value = "用户信息更新时间")
    private LocalDateTime userUpdateTime;

    /**
     * 人员姓名
     */
    @Schema(description = "人员姓名")
    @ExcelProperty(value = "人员姓名")
    private String personName;

    /**
     * 用户头像路径
     */
    @Schema(description = "用户头像路径")
    @ExcelProperty(value = "用户头像路径")
    private String userAvatarPath;

    /**
     * 用户头像地址
     */
    @Schema(description = "用户头像地址")
    @ExcelProperty(value = "用户头像地址")
    private String userAvatarUrl;

    /**
     * 会员卡号
     */
    @Schema(description = "会员卡号")
    @ExcelProperty(value = "会员卡号")
    private String memberCardNumber;

    /**
     * 用户生日时间
     */
    @Schema(description = "用户生日时间")
    @ExcelProperty(value = "用户生日时间")
    private LocalDateTime userBirthdayTime;

    /**
     * 政治面貌
     */
    @Schema(description = "政治面貌")
    @ExcelProperty(value = "政治面貌")
    private String politicalStatus;

    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称")
    @ExcelProperty(value = "用户昵称")
    private String userNickname;

    /**
     * 微信昵称
     */
    @Schema(description = "微信昵称")
    @ExcelProperty(value = "微信昵称")
    private String wechatNickname;

    /**
     * 证件类型
     */
    @Schema(description = "证件类型")
    @ExcelProperty(value = "证件类型")
    private String idCardType;

    /**
     * 用户电子邮箱
     */
    @Schema(description = "用户电子邮箱")
    @ExcelProperty(value = "用户电子邮箱")
    private String userEmail;

    /**
     * 用户地址信息
     */
    @Schema(description = "用户地址信息")
    @ExcelProperty(value = "用户地址信息")
    private String userAddress;

    /**
     * 性别（0-女，1-男）
     */
    @Schema(description = "性别（0-女，1-男）")
    @ExcelProperty(value = "性别（0-女，1-男）")
    private String gender;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    @ExcelProperty(value = "手机号码")
    private String phoneNumber;

    /**
     * 是否实名认证（0-否，1-是）
     */
    @Schema(description = "是否实名认证（0-否，1-是）")
    @ExcelProperty(value = "是否实名认证（0-否，1-是）")
    private String isRealNameVerified;

    /**
     * 用户唯一标识
     */
    @Schema(description = "用户唯一标识")
    @ExcelProperty(value = "用户唯一标识")
    private String userUniqueId;

    /**
     * 是否删除标记（0-未删除，1-已删除）
     */
    @Schema(description = "是否删除标记（0-未删除，1-已删除）")
    @ExcelProperty(value = "是否删除标记（0-未删除，1-已删除）")
    private String isDeleted;

    /**
     * 出生地区编码
     */
    @Schema(description = "出生地区编码")
    @ExcelProperty(value = "出生地区编码")
    private BigDecimal birthRegionCode;

    /**
     * 企业微信用户唯一标识
     */
    @Schema(description = "企业微信用户唯一标识")
    @ExcelProperty(value = "企业微信用户唯一标识")
    private String wechatEnterpriseUserId;

    /**
     * 用户所属国家/民族
     */
    @Schema(description = "用户所属国家/民族")
    @ExcelProperty(value = "用户所属国家/民族")
    private String userNation;

    /**
     * 会员卡卡面图片地址
     */
    @Schema(description = "会员卡卡面图片地址")
    @ExcelProperty(value = "会员卡卡面图片地址")
    private String memberCardImageUrl;

    /**
     * 身份证正面照片
     */
    @Schema(description = "身份证正面照片")
    @ExcelProperty(value = "身份证正面照片")
    private String idCardFrontPhoto;

    /**
     * 用户身份证背面照片
     */
    @Schema(description = "用户身份证背面照片")
    @ExcelProperty(value = "用户身份证背面照片")
    private String userIdCardBackPhoto;

    /**
     * 用户信息生效时间
     */
    @Schema(description = "用户信息生效时间")
    @ExcelProperty(value = "用户信息生效时间")
    private LocalDateTime userInfoEffectiveTime;

    /**
     * 用户信息结束时间
     */
    @Schema(description = "用户信息结束时间")
    @ExcelProperty(value = "用户信息结束时间")
    private LocalDateTime userInfoEndTime;

    /**
     * 组织名称或所属机构
     */
    @Schema(description = "组织名称或所属机构")
    @ExcelProperty(value = "组织名称或所属机构")
    private String organizationName;

    /**
     * 直播封面图片地址
     */
    @Schema(description = "直播封面图片地址")
    @ExcelProperty(value = "直播封面图片地址")
    private String liveCoverImageUrl;

    /**
     * 公众号用户唯一标识
     */
    @Schema(description = "公众号用户唯一标识")
    @ExcelProperty(value = "公众号用户唯一标识")
    private String wechatOfficialAccountUserId;

    /**
     * 图片索引值
     */
    @Schema(description = "图片索引值")
    @ExcelProperty(value = "图片索引值")
    private String imageIndex;

    /**
     * 操作人账号
     */
    @Schema(description = "操作人账号")
    @ExcelProperty(value = "操作人账号")
    private String operatorAccount;

    /**
     * 用户类型
     */
    @Schema(description = "用户类型")
    @ExcelProperty(value = "用户类型")
    private String userType;

    /**
     * 人员编号
     */
    @Schema(description = "人员编号")
    @ExcelProperty(value = "人员编号")
    private String personNumber;

    /**
     * 机构名称
     */
    @Schema(description = "机构名称")
    @ExcelProperty(value = "机构名称")
    private String agencyName;

    /**
     * 用户组名称
     */
    @Schema(description = "用户组名称")
    @ExcelProperty(value = "用户组名称")
    private String userGroupName;

    /**
     * 账户类型（0-个人，1-企业）
     */
    @Schema(description = "账户类型（0-个人，1-企业）")
    @ExcelProperty(value = "账户类型（0-个人，1-企业）")
    private String accountType;

    /**
     * 用户备注信息
     */
    @Schema(description = "用户备注信息")
    @ExcelProperty(value = "用户备注信息")
    private String userRemarks;

    /**
     * 企业组织ID
     */
    @Schema(description = "企业组织ID")
    @ExcelProperty(value = "企业组织ID")
    private String businessOrganizationId;

    /**
     * 注册来源渠道
     */
    @Schema(description = "注册来源渠道")
    @ExcelProperty(value = "注册来源渠道")
    private String registrationSource;
}