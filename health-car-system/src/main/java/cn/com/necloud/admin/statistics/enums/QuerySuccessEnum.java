/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 查询成功标识枚举
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Getter
@RequiredArgsConstructor
public enum QuerySuccessEnum {

    /**
     * 查询成功
     */
    SUCCESS("1", "查询成功"),

    /**
     * 查询失败
     */
    FAIL("0", "查询失败"), TOTAL("2", "合计");

    private final String code;
    private final String desc;
}