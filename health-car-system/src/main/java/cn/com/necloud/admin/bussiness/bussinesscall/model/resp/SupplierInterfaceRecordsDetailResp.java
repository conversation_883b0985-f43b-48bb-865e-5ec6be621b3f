/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.bussinesscall.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import cn.com.necloud.admin.common.model.resp.NewBaseDetailResp;
import java.io.Serial;
import java.time.*;

/**
 * 厂商接口调用记录表详情信息
 *
 * <AUTHOR>
 * @since 2025/07/11 14:36
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "厂商接口调用记录表详情信息")
public class SupplierInterfaceRecordsDetailResp extends NewBaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 供应商记录主键ID
     */
    @Schema(description = "供应商记录主键ID")
    @ExcelProperty(value = "供应商记录主键ID")
    private String businessId;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @ExcelProperty(value = "订单编号")
    private String orderNumber;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 供应商参数
     */
    @Schema(description = "供应商参数")
    @ExcelProperty(value = "供应商参数")
    private String supplierParameter;

    /**
     * 响应内容
     */
    @Schema(description = "响应内容")
    @ExcelProperty(value = "响应内容")
    private String responseContent;

    /**
     * 车辆识别代码
     */
    @Schema(description = "车辆识别代码")
    @ExcelProperty(value = "车辆识别代码")
    private String vin;

    /**
     * 供应商订单编号
     */
    @Schema(description = "供应商订单编号")
    @ExcelProperty(value = "供应商订单编号")
    private String supplierOrderNumber;

    /**
     * API名称
     */
    @Schema(description = "API名称")
    @ExcelProperty(value = "API名称")
    private String apiName;
}