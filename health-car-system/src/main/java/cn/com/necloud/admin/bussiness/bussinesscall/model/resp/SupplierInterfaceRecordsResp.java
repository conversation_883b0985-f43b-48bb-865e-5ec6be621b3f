/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.bussinesscall.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import java.io.Serial;
import java.time.*;

/**
 * 厂商接口调用记录表信息
 *
 * <AUTHOR>
 * @since 2025/07/11 14:36
 */
@Data
@Schema(description = "厂商接口调用记录表信息")
public class SupplierInterfaceRecordsResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 供应商记录主键ID
     */
    @Schema(description = "供应商记录主键ID")
    private String businessId;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名")
    private String updateUser;

    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNumber;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 供应商参数
     */
    @Schema(description = "供应商参数")
    private String supplierParameter;

    /**
     * 响应内容
     */
    @Schema(description = "响应内容")
    private String responseContent;

    /**
     * 车辆识别代码
     */
    @Schema(description = "车辆识别代码")
    private String vin;

    /**
     * 供应商订单编号
     */
    @Schema(description = "供应商订单编号")
    private String supplierOrderNumber;

    /**
     * API名称
     */
    @Schema(description = "API名称")
    private String apiName;
}