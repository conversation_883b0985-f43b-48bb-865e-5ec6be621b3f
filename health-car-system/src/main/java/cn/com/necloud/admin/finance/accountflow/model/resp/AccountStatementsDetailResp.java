/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.accountflow.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import cn.com.necloud.admin.common.model.resp.NewBaseDetailResp;
import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 账户流水详情信息
 *
 * <AUTHOR>
 * @since 2025/07/15 17:20
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "账户流水详情信息")
public class AccountStatementsDetailResp extends NewBaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键，账户报表唯一标识
     */
    @Schema(description = "业务主键，账户报表唯一标识")
    @ExcelProperty(value = "业务主键，账户报表唯一标识")
    private String businessId;

    /**
     * 对账单类型
     */
    @Schema(description = "对账单类型")
    @ExcelProperty(value = "对账单类型")
    private String statementType;

    /**
     * 应付金额，单位：元
     */
    @Schema(description = "应付金额，单位：元")
    @ExcelProperty(value = "应付金额，单位：元")
    private BigDecimal dueAmount;

    /**
     * 实际金额，单位：元
     */
    @Schema(description = "实际金额，单位：元")
    @ExcelProperty(value = "实际金额，单位：元")
    private BigDecimal actualAmount;

    /**
     * 账户id
     */
    @Schema(description = "账户id")
    @ExcelProperty(value = "账户id")
    private String accountId;

    /**
     * 账户名称
     */
    @Schema(description = "账户名称")
    @ExcelProperty(value = "账户名称")
    private String accountName;

    /**
     * 账户类型
     */
    @Schema(description = "账户类型")
    @ExcelProperty(value = "账户类型")
    private String accountType;

    /**
     * 交易前账户余额，单位：元
     */
    @Schema(description = "交易前账户余额，单位：元")
    @ExcelProperty(value = "交易前账户余额，单位：元")
    private BigDecimal balanceBeforeTrade;

    /**
     * 交易后账户余额，单位：元
     */
    @Schema(description = "交易后账户余额，单位：元")
    @ExcelProperty(value = "交易后账户余额，单位：元")
    private BigDecimal balanceAfterTrade;

    /**
     * 转账交易编号
     */
    @Schema(description = "转账交易编号")
    @ExcelProperty(value = "转账交易编号")
    private String transferTransactionId;

    /**
     * 机构名称
     */
    @Schema(description = "机构名称")
    @ExcelProperty(value = "机构名称")
    private String organizationName;

    /**
     * 付款方名称
     */
    @Schema(description = "付款方名称")
    @ExcelProperty(value = "付款方名称")
    private String payerName;

    /**
     * 发生时间
     */
    @Schema(description = "发生时间")
    @ExcelProperty(value = "发生时间")
    private LocalDateTime occurrenceTime;

    /**
     * 支付类型
     */
    @Schema(description = "支付类型")
    @ExcelProperty(value = "支付类型")
    private String paymentType;

    /**
     * 机构唯一标识
     */
    @Schema(description = "机构唯一标识")
    @ExcelProperty(value = "机构唯一标识")
    private String organizationId;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @ExcelProperty(value = "订单编号")
    private String orderNumber;

    /**
     * 区域唯一标识
     */
    @Schema(description = "区域唯一标识")
    @ExcelProperty(value = "区域唯一标识")
    private BigDecimal areaId;

    /**
     * 进账或出账
     */
    @Schema(description = "进账或出账")
    @ExcelProperty(value = "进账或出账")
    private Integer transactionType;
}