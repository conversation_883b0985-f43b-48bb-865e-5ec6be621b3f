/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.refund.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 用户订单退款记录实体
 *
 * <AUTHOR>
 * @since 2025/07/16 11:49
 */
@Data
@TableName("hc_user_order_refund_records")
public class UserOrderRefundRecordsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户退款记录主键ID
     */
    private String businessId;

    /**
     * 客户唯一标识
     */
    private String customerId;

    /**
     * 退款金额，单位：元
     */
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    private LocalDateTime refundTime;

    /**
     * 退款记录备注信息
     */
    private String refundRemark;

    /**
     * 订单编号
     */
    private String orderNumber;
}
