/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.userinterface.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 用户接口管理表信息
 *
 * <AUTHOR>
 * @since 2025/07/10 16:45
 */
@Data
@Schema(description = "用户接口管理表信息")
public class BusinessUserInterfaceRecordsResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键ID
     */
    @Schema(description = "业务主键ID")
    private String businessId;

    /**
     * 更新人名称
     */
    @Schema(description = "更新人名称")
    private String updateUser;

    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 客户唯一标识
     */
    @Schema(description = "客户唯一标识")
    private String customerId;

    /**
     * API名称
     */
    @Schema(description = "API名称")
    private String apiName;

    /**
     * state: 用户状态（0-禁用，1-启用）
     */
    @Schema(description = "state: 用户状态（0-禁用，1-启用）")
    private String userStatus;

    /**
     * 限制次数
     */
    @Schema(description = "限制次数")
    private String limitCount;

    /**
     * 活动参与次数
     */
    @Schema(description = "活动参与次数")
    private BigDecimal activityParticipationCount;

    /**
     * 价格，单位：元
     */
    @Schema(description = "价格，单位：元")
    private BigDecimal price;
}