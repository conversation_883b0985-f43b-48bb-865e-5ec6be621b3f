/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.order.mapper;

import cn.com.necloud.admin.finance.refund.model.entity.UserOrderRefundRecordsDO;
import cn.com.necloud.admin.statistics.model.resp.DataStatisticsResp;
import cn.com.necloud.admin.statistics.model.resp.RevenueStatisticsRecordResp;
import cn.com.necloud.admin.statistics.model.resp.StatisticsApiRecordsDetailResp;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.starter.data.mp.base.BaseMapper;
import cn.com.necloud.admin.bussiness.order.model.entity.BusinessUserApiOrderRecordsDO;

import java.util.List;

/**
 * 商业用户 api 调用订单表 Mapper
 *
 * <AUTHOR>
 * @since 2025/07/10 19:06
 */
@Mapper
public interface BusinessUserApiOrderRecordsMapper extends BaseMapper<BusinessUserApiOrderRecordsDO> {

    IPage<StatisticsApiRecordsDetailResp> selectStatisticsPage(@Param("page") IPage<BusinessUserApiOrderRecordsDO> page,
                                                               @Param(Constants.WRAPPER) QueryWrapper<BusinessUserApiOrderRecordsDO> queryWrapper);

    //    List<RevenueStatisticsRecordResp> selectStatisticsList(@Param(Constants.WRAPPER) QueryWrapper<UserOrderRefundRecordsDO> queryWrapper);

    List<RevenueStatisticsRecordResp> selectStatisticsList(@Param("ew") QueryWrapper<UserOrderRefundRecordsDO> wrapper,
                                                           @Param("customerIds") List<String> customerIds,
                                                           @Param("page") Page page);

    // 分页查询
    List<String> selectCustomerIdsPage(@Param("cratetime") String cratetime,
                                       @Param("customerId") String customerId,
                                       @Param("pageSize") int pageSize,
                                       @Param("offset") long offset);

    // 总数查询
    long countCustomerIds(@Param("cratetime") String cratetime, @Param("customerId") String customerId);

    List<DataStatisticsResp> selectOrderRecords(@Param("cratetime") String cratetime, @Param("endtime") String endtime);

}