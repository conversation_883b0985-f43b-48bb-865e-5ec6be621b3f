/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.base.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 车型列表信息
 *
 * <AUTHOR>
 * @since 2025/07/14 11:06
 */
@Data
@Schema(description = "车型列表信息")
public class CarModelsResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 款式主键ID
     */
    @Schema(description = "款式主键ID")
    private Long businessId;

    /**
     * 款式名称
     */
    @Schema(description = "款式名称")
    private String styleName;

    /**
     * 款式代数
     */
    @Schema(description = "款式代数")
    private Integer styleGeneration;

    /**
     * 当前建议零售价，单位：元
     */
    @Schema(description = "当前建议零售价，单位：元")
    private BigDecimal currentRetailPrice;

    /**
     * 市场类型
     */
    @Schema(description = "市场类型")
    private Integer marketType;

    /**
     * 上架时间
     */
    @Schema(description = "上架时间")
    private LocalDateTime listingTime;

    /**
     * 下架时间
     */
    @Schema(description = "下架时间")
    private LocalDateTime offlineTime;

    /**
     * 生产销售状态（0-未开始，1-进行中，2-已结束）
     */
    @Schema(description = "生产销售状态（0-未开始，1-进行中，2-已结束）")
    private Integer productionSalesStatus;

    /**
     * 启用修剪价格功能（0-禁用，1-启用）
     */
    @Schema(description = "启用修剪价格功能（0-禁用，1-启用）")
    private Integer isPriceTrimmingEnabled;

    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态（0-禁用，1-启用）")
    private Integer isEnabled;

    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 款式类型关联ID
     */
    @Schema(description = "款式类型关联ID")
    private Long associatedStyleTypeId;

    /**
     * 生产年份
     */
    @Schema(description = "生产年份")
    private String productionYear;

    /**
     * 年份
     */
    @Schema(description = "年份")
    private Integer year;

    /**
     * 停用年份
     */
    @Schema(description = "停用年份")
    private String discontinuedYear;

    /**
     * 显示名称
     */
    @Schema(description = "显示名称")
    private String displayName;

    /**
     * 平行进口类型ID
     */
    @Schema(description = "平行进口类型ID")
    private Integer parallelImportTypeId;

    /**
     * 车型名称（MPV/商务车）
     */
    @Schema(description = "车型名称（MPV/商务车）")
    private String vehicleType;

    /**
     * 新能源类型
     */
    @Schema(description = "新能源类型")
    private String newEnergyType;

    /**
     * 子模型ID，关联t_sub_model表
     */
    @Schema(description = "子模型ID，关联t_sub_model表")
    private Integer subModelId;

    /**
     * 图片本地存储路径
     */
    @Schema(description = "图片本地存储路径")
    private String imageLocalPath;

    /**
     * 图片地址
     */
    @Schema(description = "图片地址")
    private String imageUrl;

    /**
     * 图片地址
     */
    @Schema(description = "图片地址")
    private String imageUrlNew;

    /**
     * 图片本地新地址
     */
    @Schema(description = "图片本地新地址")
    private String imageLocalUrlNew;

    /**
     * 更新人名称
     */
    @Schema(description = "更新人名称")
    private String updateUser;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createUser;
}