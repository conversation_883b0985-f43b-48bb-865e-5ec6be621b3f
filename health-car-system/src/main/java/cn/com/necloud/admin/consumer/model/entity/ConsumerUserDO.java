/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.consumer.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 消费用户表实体
 *
 * <AUTHOR>
 * @since 2025/07/16 14:42
 */
@Data
@TableName("hc_consumer_user")
public class ConsumerUserDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户业务主键
     */
    private String businessId;

    /**
     * 人员姓名
     */
    private String personName;

    /**
     * 用户头像路径
     */
    private String userAvatarPath;

    /**
     * 用户头像地址
     */
    private String userAvatarUrl;

    /**
     * 会员卡号
     */
    private String memberCardNumber;

    /**
     * 用户生日时间
     */
    private LocalDateTime userBirthdayTime;

    /**
     * 政治面貌
     */
    private String politicalStatus;

    /**
     * 用户昵称
     */
    private String userNickname;

    /**
     * 微信昵称
     */
    private String wechatNickname;

    /**
     * 证件类型
     */
    private String idCardType;

    /**
     * 用户电子邮箱
     */
    private String userEmail;

    /**
     * 用户地址信息
     */
    private String userAddress;

    /**
     * 性别（0-女，1-男）
     */
    private String gender;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 是否实名认证（0-否，1-是）
     */
    private String isRealNameVerified;

    /**
     * 用户唯一标识
     */
    private String userUniqueId;

    /**
     * 是否删除标记（0-未删除，1-已删除）
     */
    private String isDeleted;

    /**
     * 出生地区编码
     */
    private BigDecimal birthRegionCode;

    /**
     * 企业微信用户唯一标识
     */
    private String wechatEnterpriseUserId;

    /**
     * 用户所属国家/民族
     */
    private String userNation;

    /**
     * 会员卡卡面图片地址
     */
    private String memberCardImageUrl;

    /**
     * 身份证正面照片
     */
    private String idCardFrontPhoto;

    /**
     * 用户身份证背面照片
     */
    private String userIdCardBackPhoto;

    /**
     * 用户信息生效时间
     */
    private LocalDateTime userInfoEffectiveTime;

    /**
     * 用户信息结束时间
     */
    private LocalDateTime userInfoEndTime;

    /**
     * 组织名称或所属机构
     */
    private String organizationName;

    /**
     * 直播封面图片地址
     */
    private String liveCoverImageUrl;

    /**
     * 公众号用户唯一标识
     */
    private String wechatOfficialAccountUserId;

    /**
     * 图片索引值
     */
    private String imageIndex;

    /**
     * 操作人账号
     */
    private String operatorAccount;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 人员编号
     */
    private String personNumber;

    /**
     * 机构名称
     */
    private String agencyName;

    /**
     * 用户组名称
     */
    private String userGroupName;

    /**
     * 账户类型（0-个人，1-企业）
     */
    private String accountType;

    /**
     * 用户备注信息
     */
    private String userRemarks;

    /**
     * 企业组织ID
     */
    private String businessOrganizationId;

    /**
     * 注册来源渠道
     */
    private String registrationSource;
}
