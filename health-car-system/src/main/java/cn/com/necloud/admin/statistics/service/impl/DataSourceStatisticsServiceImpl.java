/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.service.impl;

import cn.com.necloud.admin.bussiness.bussinesscall.mapper.SupplierInterfaceRecordsMapper;
import cn.com.necloud.admin.bussiness.order.mapper.BusinessUserApiOrderRecordsMapper;
import cn.com.necloud.admin.bussiness.productconfig.mapper.ProductConfigsMapper;
import cn.com.necloud.admin.bussiness.productconfig.model.entity.ProductConfigsDO;
import cn.com.necloud.admin.statistics.enums.ParameterEnum;
import cn.com.necloud.admin.statistics.enums.QuerySuccessEnum;
import cn.com.necloud.admin.statistics.mapper.ApiQueriesMapper;
import cn.com.necloud.admin.statistics.model.query.RevenueExpensesQuery;
import cn.com.necloud.admin.statistics.model.resp.DataStatisticsResp;
import cn.com.necloud.admin.statistics.service.DataSourceStatisticsService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataSourceStatisticsServiceImpl implements DataSourceStatisticsService {

    private final ProductConfigsMapper productConfigsMapper;
    private final SupplierInterfaceRecordsMapper supplierInterfaceRecordsMapper;
    private final BusinessUserApiOrderRecordsMapper businessUserApiOrderRecordsMapper;
    private final ApiQueriesMapper apiQueriesMapper;

    @Override
    public List<Map<String, String>> page(RevenueExpensesQuery query, PageQuery pageQuery) {
        // 参数校验
        if (query == null || Strings.isNullOrEmpty(query.getCratetime()) || Strings.isNullOrEmpty(query.getEndtime())) {
            return Collections.emptyList();
        }

        String startTime = query.getCratetime();
        String endTime = query.getEndtime();

        // 1. 查询主要数据源统计信息
        List<DataStatisticsResp> mainDataSourceStats = supplierInterfaceOrderList(startTime, endTime);
        // 2. 查询订单记录
        List<DataStatisticsResp> orderRecords = supplierOrderList(startTime, endTime);

        // 3. 合并数据源
        List<DataStatisticsResp> combinedStats = combineDataSourceStats(mainDataSourceStats, orderRecords);

        // 4. 处理鹏海历史数据
        processPengHaiHistoricalData(combinedStats, startTime, endTime);
        List<Map<String, String>> result = new ArrayList<>();
        // 5. 计算价格并生成结果
        if (combinedStats.size() > 0) {
            result = calculatePricesAndGenerateResult(combinedStats);
        }
        // 6. 添加总计行
        addTotalRow(result);

        return result;
    }

    private List<DataStatisticsResp> combineDataSourceStats(List<DataStatisticsResp> mainStats,
                                                            List<DataStatisticsResp> orderStats) {
        // 合并两个数据源，去重
        Map<String, DataStatisticsResp> combinedMap = new HashMap<>();

        // 添加主要统计数据
        mainStats.forEach(stat -> {
            String key = stat.getSuppliername() + "_" + stat.getApiname();
            combinedMap.putIfAbsent(key, stat);
        });

        // 添加订单统计数据，不覆盖已有记录
        orderStats.forEach(stat -> {
            String key = stat.getSuppliername() + "_" + stat.getApiname();
            combinedMap.putIfAbsent(key, stat);
        });

        return new ArrayList<>(combinedMap.values());
    }

    private void processPengHaiHistoricalData(List<DataStatisticsResp> stats, String startTime, String endTime) {
        LocalDate startDate = LocalDate.parse(startTime, DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate criticalDate = LocalDate.parse("2025-01-10", DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate endDate = LocalDate.parse(endTime, DateTimeFormatter.ISO_LOCAL_DATE);

        if (startDate.isAfter(criticalDate)) {
            if (criticalDate.isAfter(endDate)) {
                endTime = "2025-01-10";
            }

            List<DataStatisticsResp> historicalData = supplierInterfaceList(startTime, endTime);
            int vinAndAdditionalTotal = historicalData.stream()
                .filter(data -> ParameterEnum.VIN_ANALYSIS.getDesc()
                    .equals(data.getName()) || ParameterEnum.ADDITIONAL_QUERY.getDesc().equals(data.getName()))
                .mapToInt(data -> data.getTotal().intValue())
                .sum();

            // 更新鹏海vin解析的数据
            stats.stream()
                .filter(data -> ParameterEnum.PENGHAI.getDesc()
                    .equals(data.getSuppliername()) && ParameterEnum.VIN_ANALYSIS.getDesc().equals(data.getApiname()))
                .findFirst()
                .ifPresent(data -> {
                    int newTotal = data.getTotal().intValue() + vinAndAdditionalTotal;
                    data.setTotal(BigDecimal.valueOf(newTotal));
                    data.setPrice(BigDecimal.valueOf(newTotal * 0.28));
                });
        }
    }

    private List<Map<String, String>> calculatePricesAndGenerateResult(List<DataStatisticsResp> stats) {
        // 获取所有供应商和API名称的组合
        Set<String> supplierNames = stats.stream().map(DataStatisticsResp::getSuppliername).collect(Collectors.toSet());
        Set<String> apiNames = stats.stream().map(DataStatisticsResp::getApiname).collect(Collectors.toSet());

        // 批量查询产品配置
        Map<String, ProductConfigsDO> productConfigMap = productList(supplierNames, apiNames);

        // 计算价格并生成结果
        return stats.stream().map(stat -> {
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("suppliername", stat.getSuppliername());
            resultMap.put("apiname", stat.getApiname());
            resultMap.put("total", String.valueOf(stat.getTotal().intValue()));

            ProductConfigsDO product = productConfigMap.get(stat.getSuppliername() + "_" + stat.getApiname());
            BigDecimal price = product != null ? product.getUnitPrice() : BigDecimal.ZERO;
            BigDecimal totalPrice = stat.getTotal().multiply(price).setScale(2, RoundingMode.HALF_UP);
            resultMap.put("price", totalPrice.toString());

            return resultMap;
        }).collect(Collectors.toList());
    }

    private void addTotalRow(List<Map<String, String>> result) {
        if (!result.isEmpty()) {
            int totalCount = result.stream().mapToInt(item -> Integer.parseInt(item.get("total"))).sum();

            BigDecimal totalPrice = result.stream()
                .map(item -> new BigDecimal(item.get("price")))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            Map<String, String> totalRow = new HashMap<>();
            totalRow.put("suppliername", QuerySuccessEnum.TOTAL.getDesc());
            totalRow.put("total", String.valueOf(totalCount));
            totalRow.put("price", totalPrice.toString());
            result.add(totalRow);
        }
    }

    /*
     * @Description:  以下方法为缓存数据相关信息
     */
    @Cacheable(value = "interface_user", key = "#query.hashCode()", unless = "#result.size == 0")
    public Map<String, ProductConfigsDO> productList(Set<String> supplierNames, Set<String> apiNames) {
        return productConfigsMapper.selectList(new QueryWrapper<ProductConfigsDO>().lambda()
            .in(ProductConfigsDO::getRemarkInfo, supplierNames)
            .in(ProductConfigsDO::getApiName, apiNames))
            .stream()
            .collect(Collectors.toMap(p -> p.getRemarkInfo() + "_" + p.getApiName(), Function.identity()));
    }

    @Cacheable(value = "interface_api", key = "#query.hashCode()", unless = "#result.size == 0")
    public List<DataStatisticsResp> supplierInterfaceList(String startTime, String endTime) {
        return apiQueriesMapper.selectApi(startTime, endTime);

    }

    @Cacheable(value = "interface_orderdata", key = "#query.hashCode()", unless = "#result.size == 0")
    public List<DataStatisticsResp> supplierInterfaceOrderList(String startTime, String endTime) {
        return supplierInterfaceRecordsMapper.selectDataSourceStatistics(startTime, endTime);

    }

    @Cacheable(value = "interface_orderlist", key = "#query.hashCode()", unless = "#result.size == 0")
    public List<DataStatisticsResp> supplierOrderList(String startTime, String endTime) {
        return businessUserApiOrderRecordsMapper.selectOrderRecords(startTime, endTime);
    }

}