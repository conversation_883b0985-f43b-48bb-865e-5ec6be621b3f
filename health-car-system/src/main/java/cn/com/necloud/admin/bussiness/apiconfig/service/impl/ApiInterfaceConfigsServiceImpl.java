/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.apiconfig.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.bussiness.apiconfig.mapper.ApiInterfaceConfigsMapper;
import cn.com.necloud.admin.bussiness.apiconfig.model.entity.ApiInterfaceConfigsDO;
import cn.com.necloud.admin.bussiness.apiconfig.model.query.ApiInterfaceConfigsQuery;
import cn.com.necloud.admin.bussiness.apiconfig.model.req.ApiInterfaceConfigsReq;
import cn.com.necloud.admin.bussiness.apiconfig.model.resp.ApiInterfaceConfigsDetailResp;
import cn.com.necloud.admin.bussiness.apiconfig.model.resp.ApiInterfaceConfigsResp;
import cn.com.necloud.admin.bussiness.apiconfig.service.ApiInterfaceConfigsService;

/**
 * api接口配置表业务实现
 *
 * <AUTHOR>
 * @since 2025/07/11 14:13
 */
@Service
@RequiredArgsConstructor
public class ApiInterfaceConfigsServiceImpl extends BaseServiceImpl<ApiInterfaceConfigsMapper, ApiInterfaceConfigsDO, ApiInterfaceConfigsResp, ApiInterfaceConfigsDetailResp, ApiInterfaceConfigsQuery, ApiInterfaceConfigsReq> implements ApiInterfaceConfigsService {}