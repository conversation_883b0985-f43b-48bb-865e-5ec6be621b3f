/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.productconfig.service;

import cn.com.necloud.admin.bussiness.productconfig.model.req.ProductConfigsImportReq;
import cn.com.necloud.admin.bussiness.productconfig.model.resp.ProductConfigsImportParseResp;
import cn.com.necloud.admin.bussiness.productconfig.model.resp.ProductConfigsImportResp;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import top.continew.starter.extension.crud.service.BaseService;
import cn.com.necloud.admin.bussiness.productconfig.model.query.ProductConfigsQuery;
import cn.com.necloud.admin.bussiness.productconfig.model.req.ProductConfigsReq;
import cn.com.necloud.admin.bussiness.productconfig.model.resp.ProductConfigsDetailResp;
import cn.com.necloud.admin.bussiness.productconfig.model.resp.ProductConfigsResp;

import java.io.IOException;

/**
 * 产品信息管理业务接口
 *
 * <AUTHOR>
 * @since 2025/07/11 15:01
 */
public interface ProductConfigsService extends BaseService<ProductConfigsResp, ProductConfigsDetailResp, ProductConfigsQuery, ProductConfigsReq> {

    /**
     * 下载导入模板
     *
     * @param response 响应对象
     * @throws IOException IO异常
     */
    void downloadImportTemplate(HttpServletResponse response) throws IOException;

    /**
     * 解析导入数据
     *
     * @param file 导入文件
     * @return 解析结果
     */
    ProductConfigsImportParseResp parseImport(MultipartFile file);

    /**
     * 导入数据
     *
     * @param importReq 导入请求参数
     * @return 导入结果
     */
    ProductConfigsImportResp importData(ProductConfigsImportReq importReq);
}