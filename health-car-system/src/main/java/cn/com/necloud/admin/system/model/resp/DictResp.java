/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.system.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import cn.com.necloud.admin.common.model.resp.BaseDetailResp;

import java.io.Serial;

/**
 * 字典响应参数
 *
 * <AUTHOR>
 * @since 2023/9/11 21:29
 */
@Data
@Schema(description = "字典响应参数")
public class DictResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称", example = "公告类型")
    private String name;

    /**
     * 编码
     */
    @Schema(description = "编码", example = "notice_type")
    private String code;

    /**
     * 描述
     */
    @Schema(description = "描述", example = "公告类型描述信息")
    private String description;

    /**
     * 是否为系统内置数据
     */
    @Schema(description = "是否为系统内置数据", example = "true")
    private Boolean isSystem;
}