/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.organizational.service.impl;

import cn.com.necloud.admin.organizational.mapper.OrganizationMapper;
import cn.com.necloud.admin.organizational.model.entity.OrganizationDO;
import cn.com.necloud.admin.organizational.model.query.OrganizationQuery;
import cn.com.necloud.admin.organizational.model.req.OrganizationReq;
import cn.com.necloud.admin.organizational.model.resp.OrganizationDetailResp;
import cn.com.necloud.admin.organizational.model.resp.OrganizationResp;
import cn.com.necloud.admin.organizational.model.util.SM4;
import cn.crane4j.core.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import cn.com.necloud.admin.organizational.service.OrganizationService;

import java.util.ArrayList;
import java.util.List;

/**
 * 汽修机构表业务实现
 *
 * <AUTHOR>
 * @since 2025/07/10 17:32
 */
@Service
@RequiredArgsConstructor
public class OrganizationServiceImpl extends BaseServiceImpl<OrganizationMapper, OrganizationDO, OrganizationResp, OrganizationDetailResp, OrganizationQuery, OrganizationReq> implements OrganizationService {
    private final OrganizationMapper organizationMapper;
    private final String privateKey = "longrise12345678";

    //得重写至少俩方法一个是按联系人查询，一个是展示列表信息
    public PageResp<OrganizationResp> page(OrganizationQuery query, PageQuery pageQuery) {
        if (StringUtil.isNotEmpty(query.getPhone())) {
            try {
                String newPhone = new SM4().encrypt_cbc(query.getPhone(), privateKey);
                query.setPhone(newPhone);
            } catch (Exception e) {
                log.error("加密失败", e);
                throw new RuntimeException("加密失败");
            }

        }
        IPage<OrganizationDO> ipage = new Page<>(pageQuery.getPage(), pageQuery.getSize());
        LambdaQueryWrapper<OrganizationDO> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotEmpty(query.getIsApproved()), OrganizationDO::getIsApproved, query.getIsApproved())//审核状态
            .eq(StringUtils.isNotEmpty(query.getPhone()), OrganizationDO::getPhone, query.getPhone())
            .like(StringUtils.isNotEmpty(query.getOrganizationName()), OrganizationDO::getOrganizationName, query
                .getOrganizationName())//机构名称
            .like(StringUtils.isNotEmpty(query.getOrganizationCode()), OrganizationDO::getOrganizationCode, query
                .getOrganizationCode())//统一社会信用代码
            .likeLeft(StringUtils.isNotEmpty(query.getAreaId()), OrganizationDO::getAreaId, query.getAreaId());//组织编码;//联系人
        IPage<OrganizationDO> page = organizationMapper.selectPage(ipage, lqw);
        PageResp<OrganizationDO> pageResp = PageResp.build(page);
        PageResp<OrganizationResp> resp = new PageResp<>();
        List<OrganizationResp> organizationResps = new ArrayList<>();
        for (OrganizationDO organizationDO : pageResp.getList()) {
            OrganizationResp organizationResp = new OrganizationResp();
            BeanUtils.copyProperties(organizationDO, organizationResp);
            organizationResps.add(organizationResp);

        }
        resp.setList(organizationResps);
        resp.setTotal(pageResp.getTotal());
        return resp;

    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    public OrganizationDetailResp get(Long id) {
        if (String.valueOf(id).isEmpty()) {
            return null;
        }
        OrganizationDetailResp organizationDetailResp = new OrganizationDetailResp();
        OrganizationDO organizationDO = organizationMapper.selectById(id);
        //解密
        try {
            String newPhone = new SM4().decrypt_cbc(organizationDO.getPhone(), privateKey);
            organizationDO.setPhone(newPhone);
        } catch (Exception e) {
            log.error("解密失败", e);
            throw new RuntimeException("解密失败");
        }
        BeanUtils.copyProperties(organizationDO, organizationDetailResp);
        return organizationDetailResp;

    }
}