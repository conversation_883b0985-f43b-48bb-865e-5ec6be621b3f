/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.usertransfer.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.bussiness.usertransfer.mapper.BusinessUserTransferRecordsMapper;
import cn.com.necloud.admin.bussiness.usertransfer.model.entity.BusinessUserTransferRecordsDO;
import cn.com.necloud.admin.bussiness.usertransfer.model.query.BusinessUserTransferRecordsQuery;
import cn.com.necloud.admin.bussiness.usertransfer.model.req.BusinessUserTransferRecordsReq;
import cn.com.necloud.admin.bussiness.usertransfer.model.resp.BusinessUserTransferRecordsDetailResp;
import cn.com.necloud.admin.bussiness.usertransfer.model.resp.BusinessUserTransferRecordsResp;
import cn.com.necloud.admin.bussiness.usertransfer.service.BusinessUserTransferRecordsService;

/**
 * 商业用户转账记录表业务实现
 *
 * <AUTHOR>
 * @since 2025/07/10 17:07
 */
@Service
@RequiredArgsConstructor
public class BusinessUserTransferRecordsServiceImpl extends BaseServiceImpl<BusinessUserTransferRecordsMapper, BusinessUserTransferRecordsDO, BusinessUserTransferRecordsResp, BusinessUserTransferRecordsDetailResp, BusinessUserTransferRecordsQuery, BusinessUserTransferRecordsReq> implements BusinessUserTransferRecordsService {}