/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import java.io.Serial;
import java.time.*;

/**
 * api查询结果信息
 *
 * <AUTHOR>
 * @since 2025/07/21 10:40
 */
@Data
@Schema(description = "api查询结果信息")
public class ApiQueriesResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * API查询记录唯一标识
     */
    @Schema(description = "API查询记录唯一标识")
    private String businessId;

    /**
     * 更新人名称
     */
    @Schema(description = "更新人名称")
    private String updateUser;

    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;

    /**
     * API查询名称
     */
    @Schema(description = "API查询名称")
    private String apiQueryName;

    /**
     * 接口响应结果
     */
    @Schema(description = "接口响应结果")
    private String responseResult;

    /**
     * API请求参数
     */
    @Schema(description = "API请求参数")
    private String apiRequestParameters;

    /**
     * API查询条件排序规则
     */
    @Schema(description = "API查询条件排序规则")
    private String sortRule;
}