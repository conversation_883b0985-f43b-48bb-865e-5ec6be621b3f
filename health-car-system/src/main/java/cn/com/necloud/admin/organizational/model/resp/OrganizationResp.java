/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.organizational.model.resp;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 汽修机构表信息
 *
 * <AUTHOR>
 * @since 2025/07/10 17:32
 */
@Data
@Schema(description = "汽修机构表信息")
public class OrganizationResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String businessId;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名")
    private String updateUser;

    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除标记
     */
    @Schema(description = "是否删除标记")
    private String isDeleted;

    /**
     * 区域唯一标识
     */
    @Schema(description = "区域唯一标识")
    private BigDecimal areaId;

    /**
     * 所属区域名称
     */
    @Schema(description = "所属区域名称")
    private String areaName;

    /**
     * 组织名称
     */
    @Schema(description = "组织名称")
    private String organizationName;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String phone;

    /**
     * 联系方式
     */
    @Schema(description = "联系方式")
    private String contactMethod;

    /**
     * 法律主体名称
     */
    @Schema(description = "法律主体名称")
    private String legalName;

    /**
     * 注册时间
     */
    @Schema(description = "注册时间")
    private LocalDateTime registerTime;

    /**
     * 组织类型
     */
    @Schema(description = "组织类型")
    private String organizationType;

    /**
     * 组织唯一标识
     */
    @Schema(description = "组织唯一标识")
    private String organizationCode;

    /**
     * 组织系统编码
     */
    @Schema(description = "组织系统编码")
    private String organizationSystemCode;

    /**
     * 组织边界信息
     */
    @Schema(description = "组织边界信息")
    private String organizationBoundaryInfo;

    /**
     * 网站地址
     */
    @Schema(description = "网站地址")
    private String websiteUrl;

    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码")
    private String postalCode;

    /**
     * 地址信息
     */
    @Schema(description = "地址信息")
    private String address;

    /**
     * 密码字段，用于存储组织的登录凭证
     */
    @Schema(description = "密码字段，用于存储组织的登录凭证")
    private String password;

    /**
     * 证件照片
     */
    @Schema(description = "证件照片")
    private String certificatePhoto;

    /**
     * 认证状态（0-未认证，1-已认证）
     */
    @Schema(description = "认证状态（0-未认证，1-已认证）")
    private String authenticationStatus;

    /**
     * 企业联系电话
     */
    @Schema(description = "企业联系电话", example = "***********")
    private String legalContactPhone;

    /**
     * 机构等级
     */
    @Schema(description = "机构等级")
    private String organizationLevel;

    /**
     * 业务范围描述
     */
    @Schema(description = "业务范围描述")
    private String businessScopeDescription;

    /**
     * 注册区域代码
     */
    @Schema(description = "注册区域代码")
    private BigDecimal registrationAreaCode;

    /**
     * 注册编号
     */
    @Schema(description = "注册编号")
    private String registrationNumber;

    /**
     * 业务时间戳
     */
    @Schema(description = "业务时间戳")
    private LocalDateTime businessTimestamp;

    /**
     * 注册机构名称
     */
    @Schema(description = "注册机构名称")
    private String registrationOrganizationName;

    /**
     * 注册状态（0-未注册，1-已注册）
     */
    @Schema(description = "注册状态（0-未注册，1-已注册）")
    private String registrationStatus;

    /**
     * 注册类型
     */
    @Schema(description = "注册类型")
    private String registrationType;

    /**
     * 注册性质
     */
    @Schema(description = "注册性质")
    private String registrationNature;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型")
    private String businessType;

    /**
     * 业务等级
     */
    @Schema(description = "业务等级")
    private String businessRank;

    /**
     * 操作类型（1-新增，2-修改，3-删除）
     */
    @Schema(description = "操作类型（1-新增，2-修改，3-删除）")
    private String operationType;

    /**
     * 是否为汽车店铺
     */
    @Schema(description = "是否为汽车店铺")
    private String isCarShop;

    /**
     * 汽车制造商名称
     */
    @Schema(description = "汽车制造商名称")
    private String carManufacturerName;

    /**
     * 汽车厂商唯一标识
     */
    @Schema(description = "汽车厂商唯一标识")
    private String carManufacturerId;

    /**
     * 汽车厂商编码
     */
    @Schema(description = "汽车厂商编码")
    private String carManufacturerCode;

    /**
     * 职级类型
     */
    @Schema(description = "职级类型")
    private String jobRankType;

    /**
     * 门头照片
     */
    @Schema(description = "门头照片")
    private String doorPicture;

    /**
     * 年度限制说明
     */
    @Schema(description = "年度限制说明")
    private String annualLimitDescription;

    /**
     * 组织风貌描述
     */
    @Schema(description = "组织风貌描述")
    private String organizationDescription;

    /**
     * 监管区域面积（平方公里）
     */
    @Schema(description = "监管区域面积（平方公里）")
    private BigDecimal supervisedAreaSquareKm;

    /**
     * 附件信息（JSON数组）
     */
    @Schema(description = "附件信息（JSON数组）")
    private String attachmentInfo;

    /**
     * 是否为超级组织
     */
    @Schema(description = "是否为超级组织")
    private String isSuperOrganization;

    /**
     * 机构全称
     */
    @Schema(description = "机构全称")
    private String organizationName2;

    /**
     * 系统编码
     */
    @Schema(description = "系统编码")
    private String systemCode;

    /**
     * 应用关联用户ID
     */
    @Schema(description = "应用关联用户ID")
    private String applicationUserId;

    /**
     * 审核是否通过
     */
    @Schema(description = "审核是否通过")
    private String isApproved;

    /**
     * 审核未通过原因
     */
    @Schema(description = "审核未通过原因")
    private String reviewRejectionReason;

    /**
     * 通行证持有人姓名
     */
    @Schema(description = "通行证持有人姓名")
    private String passengerName;

    /**
     * 通行证时间戳
     */
    @Schema(description = "通行证时间戳")
    private LocalDateTime passTime;

    /**
     * 公司类型
     */
    @Schema(description = "公司类型")
    private String companyType;

    /**
     * 数据来源标识
     */
    @Schema(description = "数据来源标识")
    private String dataSource;

}