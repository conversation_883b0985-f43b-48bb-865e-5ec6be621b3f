/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-16
 * @Description:
 * @Version: 1.8
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "收支管理普通用户")
public class RevenueStatisticsRecordResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /*
     * 备注信息
     */
    @Schema(description = "id")
    private String userId;
    /*
     * 业务id
     */
    @Schema(description = "business_id")
    private String BusinessId;
    /*
     * 业务组织id
     */
    private String BusinessOrganizationId;
    /*
    
     */
    @Schema(description = "名称")
    private String name;

    @Schema(description = "备注信息")
    private String remarks;
    /*
     * 客户唯一标识
     */
    @Schema(description = "客户唯一标识")
    private String customerId;
    /*
     * 客户名称
     */
    @Schema(description = "api名称")
    private String apiname;
    /*
     * 查询量
     */
    @Schema(description = "查询量")
    private Integer totalamount;
    /*
     * 查询成功数据量
     */
    @Schema(description = "查询成功数据量")
    private Integer successmount;
    /*
     * 查询失败数据量
     */
    @Schema(description = "查询失败数据量")
    private Integer failmount;
    /*
     * 收入
     */
    @Schema(description = "收入")
    private BigDecimal revenue;
    /*
     * 成本
     */
    @Schema(description = "成本")
    private BigDecimal costprice;
    /*
     * 毛利
     */
    @Schema(description = "毛利")
    private BigDecimal gross;
    //差个收入不含税
}
