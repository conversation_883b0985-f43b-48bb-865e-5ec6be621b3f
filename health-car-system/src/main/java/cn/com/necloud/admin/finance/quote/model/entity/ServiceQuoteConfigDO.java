/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.quote.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;

/**
 * 服务报价配置表实体
 *
 * <AUTHOR>
 * @since 2025/07/15 17:40
 */
@Data
@TableName("hc_service_quote_config")
public class ServiceQuoteConfigDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（jkycfwjeconf表唯一标识）
     */
    private String businessId;

    /**
     * 原价格，单位：元
     */
    private String originalPrice;

    /**
     * 商品新价格，单位：元
     */
    private String newPrice;

    /**
     * 查询类型（如实时查询、历史查询等）
     */
    private String queryType;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 免费标识（0-不免费，1-免费）
     */
    private String isFree;

    /**
     * 会员价格，单位：元
     */
    private String memberPrice;

    /**
     * 一次性充值2万元
     */
    private String oneTimeRechargeAmountTwoW;

    /**
     * 一次性充值5万元
     */
    private String oneTimeRechargeAmountFiveW;
}
