/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.order.mapper;

import cn.com.necloud.admin.statistics.model.resp.RevenueStatisticsRecordResp;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import top.continew.starter.data.mp.base.BaseMapper;
import cn.com.necloud.admin.order.model.entity.ConsumptionRecordsDO;

import java.util.List;

@Mapper
/**
 * 消费记录表 Mapper
 *
 * <AUTHOR>
 * @since 2025/07/10 14:45
 */
public interface ConsumptionRecordsMapper extends BaseMapper<ConsumptionRecordsDO> {
    List<RevenueStatisticsRecordResp> selectTableReccord(@Param(Constants.WRAPPER) QueryWrapper<ConsumptionRecordsDO> queryRecordsWrapper);
}