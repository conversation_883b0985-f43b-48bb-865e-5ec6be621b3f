/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.statistics.mapper.ApiQueriesMapper;
import cn.com.necloud.admin.statistics.model.entity.ApiQueriesDO;
import cn.com.necloud.admin.statistics.model.query.ApiQueriesQuery;
import cn.com.necloud.admin.statistics.model.req.ApiQueriesReq;
import cn.com.necloud.admin.statistics.model.resp.ApiQueriesDetailResp;
import cn.com.necloud.admin.statistics.model.resp.ApiQueriesResp;
import cn.com.necloud.admin.statistics.service.ApiQueriesService;

/**
 * api查询结果业务实现
 *
 * <AUTHOR>
 * @since 2025/07/21 10:40
 */
@Service
@RequiredArgsConstructor
public class ApiQueriesServiceImpl extends BaseServiceImpl<ApiQueriesMapper, ApiQueriesDO, ApiQueriesResp, ApiQueriesDetailResp, ApiQueriesQuery, ApiQueriesReq> implements ApiQueriesService {}