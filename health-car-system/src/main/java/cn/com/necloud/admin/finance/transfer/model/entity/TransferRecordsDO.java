/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.transfer.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 转账登记记录表实体
 *
 * <AUTHOR>
 * @since 2025/07/15 16:57
 */
@Data
@TableName("hc_transfer_records")
public class TransferRecordsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String businessId;

    /**
     * 资金来源说明
     */
    private String fundSourceDescription;

    /**
     * 转入机构名称
     */
    private String transferInOrganizationName;

    /**
     * 转出机构名称
     */
    private String transferOutOrganizationName;

    /**
     * 付款方名称
     */
    private String payerName;

    /**
     * 付款人手机号码
     */
    private String payerPhoneNumber;

    /**
     * 转账总金额，单位：元
     */
    private BigDecimal totalAmount;

    /**
     * 资金用途说明
     */
    private String fundUsageDescription;

    /**
     * 转账银行名称
     */
    private String transferBankName;

    /**
     * 转账到账时间
     */
    private LocalDateTime transferReceiveTime;

    /**
     * 客服人员账号
     */
    private String customerServiceStaff;

    /**
     * 支付类型
     */
    private String paymentType;

    /**
     * 转账凭证附件
     */
    private String transferVoucherAttachment;

    /**
     * 转账备注信息
     */
    private String transferRemark;

    /**
     * 注册时间
     */
    private LocalDateTime registrationTime;

    /**
     * 转账状态（0-失败，1-成功，2-处理中）
     */
    private String transferStatus;

    /**
     * 机构唯一标识
     */
    private String organizationId;

    /**
     * 转入机构ID
     */
    private String transferInOrganizationId;

    /**
     * 地区编号
     */
    private BigDecimal areaId;
}
