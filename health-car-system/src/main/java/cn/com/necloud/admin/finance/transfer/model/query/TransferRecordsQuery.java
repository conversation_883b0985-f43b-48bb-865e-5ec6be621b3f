/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.transfer.model.query;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;
import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.util.Date;
import java.util.List;

/**
 * 转账登记记录表查询条件
 *
 * <AUTHOR>
 * @since 2025/07/15 16:57
 */
@Data
@Schema(description = "转账登记记录表查询条件")
public class TransferRecordsQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 通过机构名称进行查询
     */
    @Schema(description = "通过机构名称进行查询")
    @Query(type = QueryType.LIKE)
    private String organizationName;

    /**
     * 通过付款方式进行查询
     */
    @Schema(description = "通过付款方式进行查询")
    @Query(type = QueryType.LIKE)
    private String paymentType;

    /**
     * 通过到款日期范围进行查询
     */
    @Schema(description = "通过到款日期范围进行查询")
    @Query(type = QueryType.BETWEEN)
    private List<Date> transferReceiveTime;

    /**
     * 通过登记时间范围进行查询
     */
    @Schema(description = "通过登记时间范围进行查询")
    @Query(type = QueryType.BETWEEN)
    private List<Date> registrationTime;

    /**
     * 通过转账状态进行查询
     */
    @Schema(description = "通过转账状态进行查询")
    @Query(type = QueryType.EQ)
    private String transferStatus;

}