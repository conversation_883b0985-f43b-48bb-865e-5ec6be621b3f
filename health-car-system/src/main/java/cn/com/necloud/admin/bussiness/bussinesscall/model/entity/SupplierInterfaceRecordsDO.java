/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.bussinesscall.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;

/**
 * 厂商接口调用记录表实体
 *
 * <AUTHOR>
 * @since 2025/07/11 14:36
 */
@Data
@TableName("hc_supplier_interface_records")
public class SupplierInterfaceRecordsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 供应商记录主键ID
     */
    private String businessId;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商参数
     */
    private String supplierParameter;

    /**
     * 响应内容
     */
    private String responseContent;

    /**
     * 车辆识别代码
     */
    private String vin;

    /**
     * 供应商订单编号
     */
    private String supplierOrderNumber;

    /**
     * API名称
     */
    private String apiName;
}
