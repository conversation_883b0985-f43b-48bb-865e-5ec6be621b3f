/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.service.impl;

import cn.com.necloud.admin.bussiness.order.mapper.BusinessUserApiOrderRecordsMapper;
import cn.com.necloud.admin.bussiness.order.model.entity.BusinessUserApiOrderRecordsDO;
import cn.com.necloud.admin.bussiness.productconfig.mapper.ProductConfigsMapper;
import cn.com.necloud.admin.bussiness.productconfig.model.entity.ProductConfigsDO;
import cn.com.necloud.admin.consumer.mapper.ConsumerUserMapper;
import cn.com.necloud.admin.consumer.model.entity.ConsumerUserDO;
import cn.com.necloud.admin.finance.refund.model.entity.UserOrderRefundRecordsDO;
import cn.com.necloud.admin.order.mapper.ConsumptionRecordsMapper;
import cn.com.necloud.admin.order.model.entity.ConsumptionRecordsDO;
import cn.com.necloud.admin.statistics.enums.ParameterEnum;
import cn.com.necloud.admin.statistics.enums.QuerySuccessEnum;
import cn.com.necloud.admin.statistics.enums.UserOderEnum;
import cn.com.necloud.admin.statistics.model.query.RevenueExpensesQuery;
import cn.com.necloud.admin.statistics.model.resp.RevenueExpensesRecordsDetailResp;
import cn.com.necloud.admin.statistics.model.resp.RevenueRecordsResp;
import cn.com.necloud.admin.statistics.model.resp.RevenueStatisticsRecordResp;
import cn.com.necloud.admin.statistics.model.util.PageResult;
import cn.com.necloud.admin.statistics.model.util.PageRevenue;
import cn.com.necloud.admin.statistics.service.RevenueExpensesService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-16
 * @Description:
 * @Version: 1.8
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RevenueExpensesServiceImpl implements RevenueExpensesService {

    /*  引入订单表
     */
    private final BusinessUserApiOrderRecordsMapper businessUserApiOrderRecordsMapper;
    private final ProductConfigsMapper productConfigsMapper;
    private final ConsumptionRecordsMapper consumptionRecordsMapper;
    private final ConsumerUserMapper consumerUserMapper;

    /**
     * 分页查询收支管理 普通用户
     *
     * @param query
     * @param pageQuery
     * @return
     */
    @Override
    public PageResult page(RevenueExpensesQuery query, PageQuery pageQuery) {
        List<RevenueStatisticsRecordResp> recordResps = new ArrayList<>();
        List<RevenueExpensesRecordsDetailResp> result = new ArrayList<>();
        long offset = (long)(pageQuery.getPage() - 1) * pageQuery.getSize();
        // 1. 分页查询customerIds
        List<String> customerIds = listCustomerIds(query, pageQuery, offset);
        long total = businessUserApiOrderRecordsMapper.countCustomerIds(query.getCratetime(), query.getCustomerId());
        Page<RevenueStatisticsRecordResp> mpPage = new Page<>(pageQuery.getPage(), pageQuery.getSize());
        // 2. 构建查询条件
        QueryWrapper<UserOrderRefundRecordsDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
            .ge(StringUtil.isNotEmpty(query.getCratetime()), UserOrderRefundRecordsDO::getCreateTime, query
                .getCratetime())
            .le(StringUtil.isNotEmpty(query.getEndtime()), UserOrderRefundRecordsDO::getRefundTime, query.getEndtime());
        // 3. 执行分页查询
        recordResps = statisticsList(wrapper, customerIds, mpPage);
        // 初始化合计行数据
        int totalAmountSum = 0;
        int successMountSum = 0;
        int failMountSum = 0;
        BigDecimal revenueSum = BigDecimal.ZERO;
        BigDecimal costSum = BigDecimal.ZERO;
        BigDecimal incomeNoTaxSum = BigDecimal.ZERO;
        BigDecimal profitSum = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(customerIds) && CollectionUtil.isNotEmpty(recordResps)) {
            // 3. 按 customerId 分组
            Map<String, List<RevenueStatisticsRecordResp>> customerOrderMap = recordResps.stream()
                .collect(Collectors.groupingBy(RevenueStatisticsRecordResp::getCustomerId));
            // 4. 遍历当前页用户，计算统计数据
            for (String customerId : customerIds) {
                RevenueExpensesRecordsDetailResp rsp = new RevenueExpensesRecordsDetailResp();
                int totalamount = 0;
                int successmount = 0;
                int failmount = 0;
                BigDecimal revenue = BigDecimal.ZERO;
                BigDecimal cost = BigDecimal.ZERO;
                // 判断是否是测试用户（userid包含test或ceshi）
                boolean isTestUser = customerId.contains(UserOderEnum.MINI_PROGRAM.getCode()) || customerId
                    .equals(UserOderEnum.TEST_MINI_PROGRAM.getCode());
                List<RevenueStatisticsRecordResp> userOrders = customerOrderMap.getOrDefault(customerId, Collections
                    .emptyList());
                for (RevenueStatisticsRecordResp order : userOrders) {
                    totalamount += order.getTotalamount();
                    successmount += order.getSuccessmount();
                    failmount += order.getFailmount();
                    // 测试用户不计入收入
                    if (!isTestUser) {
                        revenue = revenue.add(ObjectUtil.defaultIfNull(order.getRevenue(), BigDecimal.ZERO));
                    }
                    // 计算成本（所有用户都计算成本）
                    QueryWrapper<ProductConfigsDO> queryConfigsWrapper = new QueryWrapper<>();
                    queryConfigsWrapper.lambda()
                        .eq(ProductConfigsDO::getRemarkInfo, order.getRemarks())
                        .eq(ProductConfigsDO::getApiName, order.getApiname());
                    ProductConfigsDO productConfigsDO = productConfigsMapper.selectOne(queryConfigsWrapper, true);
                    if (productConfigsDO != null && productConfigsDO.getUnitPrice() != null) {
                        BigDecimal unitPrice = productConfigsDO.getUnitPrice();
                        BigDecimal orderCost = new BigDecimal(order.getSuccessmount()).multiply(unitPrice)
                            .setScale(2, RoundingMode.HALF_UP);
                        cost = cost.add(orderCost);
                    }
                }
                // 累加合计行数据（当前页）
                totalAmountSum += totalamount;
                successMountSum += successmount;
                failMountSum += failmount;
                // 只有非测试用户才计入总收入
                if (!isTestUser) {
                    revenueSum = revenueSum.add(revenue);
                }
                costSum = costSum.add(cost); // 成本所有用户都计入
                // 设置用户数据
                rsp.setUserId(customerId);
                rsp.setQueryCount(totalamount);
                rsp.setQuerySuccessCount(successmount);
                rsp.setQueryFailCount(failmount);
                // 测试用户的收入相关字段设为0
                if (isTestUser) {
                    rsp.setIncome(BigDecimal.ZERO);
                    rsp.setIncomeNoTax(BigDecimal.ZERO);
                    rsp.setProfit(BigDecimal.ZERO);
                } else {
                    rsp.setIncome(revenue);
                    // 计算不含税收入
                    BigDecimal incomeNoTax = BigDecimal.ZERO;
                    if (revenue.compareTo(BigDecimal.ZERO) > 0) {
                        incomeNoTax = revenue.divide(new BigDecimal("1.01"), 2, RoundingMode.HALF_UP);
                        incomeNoTaxSum = incomeNoTaxSum.add(incomeNoTax);
                    }
                    rsp.setIncomeNoTax(incomeNoTax);
                    // 计算毛利率
                    BigDecimal profit = BigDecimal.ZERO;
                    if (revenue.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal grossProfit = revenue.subtract(cost);
                        profit = grossProfit.divide(revenue, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"))
                            .setScale(2, RoundingMode.HALF_UP);
                    }
                    rsp.setProfit(profit);
                }
                rsp.setCost(cost); // 成本所有用户都显示

                result.add(rsp);
            }
            // 添加当前页合计行
            RevenueExpensesRecordsDetailResp totalRow = new RevenueExpensesRecordsDetailResp();
            totalRow.setUserId(QuerySuccessEnum.TOTAL.getDesc());
            totalRow.setQueryCount(totalAmountSum);
            totalRow.setQuerySuccessCount(successMountSum);
            totalRow.setQueryFailCount(failMountSum);
            totalRow.setIncome(revenueSum);
            totalRow.setCost(costSum);
            totalRow.setIncomeNoTax(incomeNoTaxSum);
            if (revenueSum.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal grossProfitSum = revenueSum.subtract(costSum);
                BigDecimal grossMarginSum = grossProfitSum.divide(revenueSum, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, RoundingMode.HALF_UP);
                totalRow.setProfit(grossMarginSum);
            } else {
                totalRow.setProfit(BigDecimal.ZERO);
            }
            result.add(totalRow);
        }

        // 返回分页结果
        PageResult pageResult = new PageResult();
        pageResult.setTotal(total);
        pageResult.setList(result);
        pageResult.setPageNum(pageQuery.getPage());
        pageResult.setPageSize(pageQuery.getSize());
        return pageResult;
    }

    /**
     * @Description: 收支管理服务实现类
     * @Author: zhongxiaoxiao
     * @Date: 2020/1/2 10:05
     */
    @Override
    public PageRevenue pageProgram(RevenueExpensesQuery query, PageQuery pageQuery) {
        PageRevenue pageRevenue = new PageRevenue();
        pageRevenue.setPageNum(pageQuery.getPage());
        pageRevenue.setPageSize(pageQuery.getSize());
        IPage<ConsumptionRecordsDO> consumptionPage = selectTableReccord(query, pageQuery);
        if (CollUtil.isEmpty(consumptionPage.getRecords())) {
            return pageRevenue;
        }
        // 3. 批量获取关联数据
        List<String> orderNumbers = consumptionPage.getRecords()
            .stream()
            .map(ConsumptionRecordsDO::getQueryOrderNumber)
            .collect(Collectors.toList());
        // 批量查询订单信息
        Map<String, BusinessUserApiOrderRecordsDO> orderMap = busOderList(orderNumbers);

        // 4. 批量查询产品配置
        Set<String> remarks = orderMap.values()
            .stream()
            .map(BusinessUserApiOrderRecordsDO::getRemarks)
            .collect(Collectors.toSet());
        Set<String> apiNames = orderMap.values()
            .stream()
            .map(BusinessUserApiOrderRecordsDO::getApiName)
            .collect(Collectors.toSet());
        /*
         * 批量查询产品配置
         */
        Map<String, ProductConfigsDO> productConfigMap = productList(remarks, apiNames);
        // 5. 批量查询用户信息
        List<String> userIds = consumptionPage.getRecords()
            .stream()
            .map(ConsumptionRecordsDO::getUserId)
            .distinct()
            .collect(Collectors.toList());

        Map<String, ConsumerUserDO> userMap = userInfoList(userIds);
        // 6. 按机构分组统计
        Map<String, RevenueRecordsResp> agencyStats = new HashMap<>();
        BigDecimal totalRevenue = BigDecimal.ZERO;
        BigDecimal totalCost = BigDecimal.ZERO;
        int totalCount = 0;
        int totalSuccess = 0;
        int totalFail = 0;

        for (ConsumptionRecordsDO record : consumptionPage.getRecords()) {
            BusinessUserApiOrderRecordsDO order = orderMap.get(record.getQueryOrderNumber());
            if (order == null)
                continue;

            ProductConfigsDO product = productConfigMap.get(order.getRemarks() + "_" + order.getApiName());
            ConsumerUserDO user = userMap.get(record.getUserId());

            String agencyName = determineAgencyName(user); // 确定机构名称

            RevenueRecordsResp agencyStat = agencyStats.computeIfAbsent(agencyName, k -> {
                RevenueRecordsResp resp = new RevenueRecordsResp();
                resp.setUserName(agencyName);  // 这里设置name值
                // 建议初始化其他必要字段
                resp.setQueryCount(0);
                resp.setQuerySuccessCount(0);
                resp.setQueryFailCount(0);
                resp.setIncomeNoTax(BigDecimal.ZERO);
                resp.setIncome(BigDecimal.ZERO);
                resp.setProfit(BigDecimal.ZERO);
                resp.setCost(BigDecimal.ZERO);
                resp.setIncome(BigDecimal.ZERO);
                resp.setCost(BigDecimal.ZERO);
                return resp;
            });

            // 统计计算
            BigDecimal cost = product != null ? product.getUnitPrice() : BigDecimal.ZERO;
            BigDecimal revenue = record.getPaymentAmount() != null ? record.getPaymentAmount() : BigDecimal.ZERO;

            agencyStat.setQueryCount(agencyStat.getQueryCount() + 1);
            if (ParameterEnum.PAY_SUCCESS.getCode().equals(record.getPaymentStatus())) {
                agencyStat.setQuerySuccessCount(agencyStat.getQuerySuccessCount() + 1);
                agencyStat.setIncome(agencyStat.getIncome().add(revenue));
                agencyStat.setCost(agencyStat.getCost().add(cost));
            } else {
                agencyStat.setQueryFailCount(agencyStat.getQueryFailCount() + 1);
            }

            // 累计总计
            totalCount++;
            if (ParameterEnum.PAY_SUCCESS.getCode().equals(record.getPaymentStatus())) {
                totalSuccess++;
                totalRevenue = totalRevenue.add(revenue);
                totalCost = totalCost.add(cost);
            } else {
                totalFail++;
            }
        }

        // 7. 计算衍生字段（不含税收入、毛利率等）
        agencyStats.values().forEach(this::calculateDerivedFields);

        // 8. 添加合计行
        RevenueRecordsResp totalRow = new RevenueRecordsResp();
        totalRow.setUserName(QuerySuccessEnum.TOTAL.getDesc());
        totalRow.setQueryCount(totalCount);
        totalRow.setQuerySuccessCount(totalSuccess);
        totalRow.setQueryFailCount(totalFail);
        totalRow.setIncome(totalRevenue);
        totalRow.setCost(totalCost);
        calculateDerivedFields(totalRow);

        // 9. 构建返回结果
        List<RevenueRecordsResp> result = new ArrayList<>(agencyStats.values());// 添加合计行
        pageRevenue.setList(result);
        pageRevenue.setTotal(consumptionPage.getTotal());
        return pageRevenue;
    }

    // 辅助方法：确定机构名称
    private String determineAgencyName(ConsumerUserDO user) {
        if (user == null) {
            return UserOderEnum.UNKNOWNUSER.getDesc();
        }
        if (StringUtil.isEmpty(user.getAgencyName())) {
            return StringUtil.isEmpty(user.getBusinessOrganizationId())
                ? UserOderEnum.MINI_PROGRAM.getDesc()
                : UserOderEnum.TEST_MINI_PROGRAM.getDesc();
        }
        return user.getAgencyName();
    }

    // 辅助方法：计算衍生字段（不含税收入、毛利率等）
    private void calculateDerivedFields(RevenueRecordsResp record) {
        // 计算不含税收入（税率1%）
        if (record.getIncome().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal incomeNoTax = record.getIncome().divide(new BigDecimal("1.01"), 2, RoundingMode.HALF_UP);
            record.setIncomeNoTax(incomeNoTax);

            // 计算毛利率
            BigDecimal grossProfit = record.getIncome().subtract(record.getCost());
            BigDecimal grossMargin = grossProfit.divide(record.getIncome(), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.HALF_UP);
            record.setProfit(grossMargin);
        } else {
            record.setIncomeNoTax(BigDecimal.ZERO);
            record.setProfit(BigDecimal.ZERO);
        }
    }

    /**
     * 订单数据查询
     *
     * @param query     查询参数
     * @param pageQuery 分页参数
     * @return 结果
     */
    @Cacheable(value = "interface_customerId", key = "#query.hashCode()", unless = "#result.size == 0")
    public List<String> listCustomerIds(RevenueExpensesQuery query, PageQuery pageQuery, long offset) {
        return businessUserApiOrderRecordsMapper.selectCustomerIdsPage(query.getCratetime(), query
            .getCustomerId(), pageQuery.getSize(), offset);
    }

    /**
     * 订单数据查询
     *
     * @param customerIds 查询参数
     * @param mpPage      分页参数
     * @return 订单列表
     */
    @Cacheable(value = "interface_record", key = "#query.hashCode()", unless = "#result.size == 0")
    public List<RevenueStatisticsRecordResp> statisticsList(QueryWrapper<UserOrderRefundRecordsDO> wrapper,
                                                            List<String> customerIds,
                                                            Page mpPage) {
        return businessUserApiOrderRecordsMapper.selectStatisticsList(wrapper, customerIds, mpPage);
    }

    /**
     * 小程序订单数据查询
     *
     * @param query     查询参数
     * @param pageQuery 分页参数
     * @return 用户列表
     */
    @Cacheable(value = "interface_consump", key = "#query.hashCode()", unless = "#result.size == 0")
    public Page<ConsumptionRecordsDO> selectTableReccord(RevenueExpensesQuery query, PageQuery pageQuery) {
        QueryWrapper<ConsumptionRecordsDO> baseWrapper = new QueryWrapper<>();
        baseWrapper.lambda()
            .ge(StringUtil.isNotEmpty(query.getCratetime()), ConsumptionRecordsDO::getCreateTime, query.getCratetime())
            .lt(StringUtil.isNotEmpty(query.getEndtime()), ConsumptionRecordsDO::getCreateTime, query.getEndtime())
            .eq(ConsumptionRecordsDO::getPaymentStatus, ParameterEnum.PAY_SUCCESS.getCode());

        // 2. 分页查询消费记录
        Page<ConsumptionRecordsDO> page = new Page<>(pageQuery.getPage(), pageQuery.getSize());
        return consumptionRecordsMapper.selectPage(page, baseWrapper);
    }

    // 订单数据查询
    @Cacheable(value = "interface_order", key = "#query.hashCode()", unless = "#result.size == 0")
    public Map<String, BusinessUserApiOrderRecordsDO> busOderList(List<String> orderNumbers) {
        return businessUserApiOrderRecordsMapper.selectList(new QueryWrapper<BusinessUserApiOrderRecordsDO>().lambda()
            .in(BusinessUserApiOrderRecordsDO::getOrderNumber, orderNumbers))
            .stream()
            .collect(Collectors.toMap(BusinessUserApiOrderRecordsDO::getOrderNumber, Function.identity()));
    }

    //产品查询
    @Cacheable(value = "interface_product", key = "#query.hashCode()", unless = "#result.size == 0")
    public Map<String, ProductConfigsDO> productList(Set<String> remarks, Set<String> apiNames) {
        return productConfigsMapper.selectList(new QueryWrapper<ProductConfigsDO>().lambda()
            .in(ProductConfigsDO::getRemarkInfo, remarks)
            .in(ProductConfigsDO::getApiName, apiNames))
            .stream()
            .collect(Collectors.toMap(p -> p.getRemarkInfo() + "_" + p.getApiName(), Function.identity()));
    }

    //用户查询
    @Cacheable(value = "interface_user", key = "#query.hashCode()", unless = "#result.size == 0")
    public Map<String, ConsumerUserDO> userInfoList(List<String> userIds) {
        return consumerUserMapper.selectList(new QueryWrapper<ConsumerUserDO>().lambda()
            .in(ConsumerUserDO::getBusinessId, userIds))
            .stream()
            .collect(Collectors.toMap(ConsumerUserDO::getBusinessId, Function.identity()));
    }
}
