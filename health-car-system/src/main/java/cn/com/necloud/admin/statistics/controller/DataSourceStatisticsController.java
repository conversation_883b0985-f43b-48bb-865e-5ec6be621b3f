/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.controller;

import cn.com.necloud.admin.statistics.model.query.RevenueExpensesQuery;
import cn.com.necloud.admin.statistics.service.DataSourceStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.starter.extension.crud.model.query.PageQuery;

import java.util.List;
import java.util.Map;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-20
 * @Description:
 * @Version: 1.8
 */
@Tag(name = "统计数据源 ")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/statists")
public class DataSourceStatisticsController {

    private final DataSourceStatisticsService dataSourceStatisticsService;

    /**
     * 统计数据源 表有 supplierrecord、jkycrecallback和jkycapiorder
     *
     * @return
     */

    @Operation(summary = "数据源统计管理", description = "数据源统计管理")
    @RequestMapping("/dataSource")
    public List<Map<String, String>> dataSourceStatistics(RevenueExpensesQuery query, @Validated PageQuery pageQuery) {
        return dataSourceStatisticsService.page(query, pageQuery);
    }
}
