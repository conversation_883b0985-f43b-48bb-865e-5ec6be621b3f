/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-16
 * @Description: 收支统计模型实体对象
 * @Version: 1.8
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "收支管理")
public class RevenueExpensesRecordsDetailResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 用户
     */
    @Schema(description = "用户")
    private String userName;
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;
    /**
     * 查询量
     */
    @Schema(description = "查询量")
    private Integer queryCount;
    /**
     * 查询成功数量
     */
    @Schema(description = "查询成功数量")
    private Integer querySuccessCount;
    /**
     * 查询失败数量
     */
    @Schema(description = "查询失败数量")
    private Integer queryFailCount;
    /**
     * 收入
     */
    @Schema(description = "收入")
    private BigDecimal income;
    /**
     * 收入（不含税）
     */
    @Schema(description = "收入（不含税）")
    private BigDecimal incomeNoTax;
    /**
     * 成本
     */
    @Schema(description = "成本")
    private BigDecimal cost;
    /**
     * 毛利
     */
    @Schema(description = "毛利")
    private BigDecimal profit;
}
