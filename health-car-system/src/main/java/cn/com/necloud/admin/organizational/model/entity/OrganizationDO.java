/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.organizational.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 汽修机构表实体
 *
 * <AUTHOR>
 * @since 2025/07/10 17:32
 */
@Data
@TableName("hc_organization")
public class OrganizationDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String businessId;

    /**
     * 是否删除标记
     */
    private String isDeleted;

    /**
     * 区域唯一标识
     */
    private BigDecimal areaId;

    /**
     * 所属区域名称
     */
    private String areaName;

    /**
     * 组织名称
     */
    private String organizationName;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 联系方式
     */
    private String contactMethod;

    /**
     * 法律主体名称
     */
    private String legalName;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 组织类型
     */
    private String organizationType;

    /**
     * 组织唯一标识
     */
    private String organizationCode;

    /**
     * 组织系统编码
     */
    private String organizationSystemCode;

    /**
     * 组织边界信息
     */
    private String organizationBoundaryInfo;

    /**
     * 网站地址
     */
    private String websiteUrl;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 地址信息
     */
    private String address;

    /**
     * 密码字段，用于存储组织的登录凭证
     */
    private String password;

    /**
     * 证件照片
     */
    private String certificatePhoto;

    /**
     * 认证状态（0-未认证，1-已认证）
     */
    private String authenticationStatus;

    /**
     * 企业联系电话
     */
    private String legalContactPhone;

    /**
     * 机构等级
     */
    private String organizationLevel;

    /**
     * 业务范围描述
     */
    private String businessScopeDescription;

    /**
     * 注册区域代码
     */
    private BigDecimal registrationAreaCode;

    /**
     * 注册编号
     */
    private String registrationNumber;

    /**
     * 业务时间戳
     */
    private LocalDateTime businessTimestamp;

    /**
     * 注册机构名称
     */
    private String registrationOrganizationName;

    /**
     * 注册状态（0-未注册，1-已注册）
     */
    private String registrationStatus;

    /**
     * 注册类型
     */
    private String registrationType;

    /**
     * 注册性质
     */
    private String registrationNature;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务等级
     */
    private String businessRank;

    /**
     * 操作类型（1-新增，2-修改，3-删除）
     */
    private String operationType;

    /**
     * 是否为汽车店铺
     */
    private String isCarShop;

    /**
     * 汽车制造商名称
     */
    private String carManufacturerName;

    /**
     * 汽车厂商唯一标识
     */
    private String carManufacturerId;

    /**
     * 汽车厂商编码
     */
    private String carManufacturerCode;

    /**
     * 职级类型
     */
    private String jobRankType;

    /**
     * 门头照片
     */
    private String doorPicture;

    /**
     * 年度限制说明
     */
    private String annualLimitDescription;

    /**
     * 组织风貌描述
     */
    private String organizationDescription;

    /**
     * 监管区域面积（平方公里）
     */
    private BigDecimal supervisedAreaSquareKm;

    /**
     * 附件信息（JSON数组）
     */
    private String attachmentInfo;

    /**
     * 是否为超级组织
     */
    private String isSuperOrganization;

    /**
     * 机构全称
     */
    private String pname;

    /**
     * 系统编码
     */
    private String systemCode;

    /**
     * 应用关联用户ID
     */
    private String applicationUserId;

    /**
     * 审核是否通过
     */
    private String isApproved;

    /**
     * 审核未通过原因
     */
    private String reviewRejectionReason;

    /**
     * 通行证持有人姓名
     */
    private String passengerName;

    /**
     * 通行证时间戳
     */
    private LocalDateTime passTime;

    /**
     * 公司类型
     */
    private String companyType;

    /**
     * 数据来源标识
     */
    private String dataSource;
}
