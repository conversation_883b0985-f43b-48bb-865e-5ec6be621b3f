/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.groupservice.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import java.io.Serial;
import java.time.*;

/**
 * 用户服务报价配置表信息
 *
 * <AUTHOR>
 * @since 2025/07/16 11:19
 */
@Data
@Schema(description = "用户服务报价配置表信息")
public class UserServiceQuoteConfigsResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键，客户配置唯一标识
     */
    @Schema(description = "业务主键，客户配置唯一标识")
    private String businessId;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名")
    private String updateUser;

    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 客户分组名称
     */
    @Schema(description = "客户分组名称")
    private String customerGroupName;

    /**
     * 服务价格，单位：元
     */
    @Schema(description = "服务价格，单位：元")
    private String servicePrice;

    /**
     * 查询类型
     */
    @Schema(description = "查询类型")
    private String queryType;

    /**
     * 渠道类型
     */
    @Schema(description = "渠道类型")
    private String channelType;
}