/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 用户渠道枚举
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Getter
@RequiredArgsConstructor
public enum UserOderEnum {

    /**
     * API用户
     */
    UNKNOWNUSER("1", "未知用户"),

    /**
     * 小程序用户
     */
    MINI_PROGRAM("test", "小程序用户"), TEST_MINI_PROGRAM("ceshi", "小程序测试用户");

    private final String code;
    private final String desc;

    /**
     * 根据代码获取枚举
     */
    public static UserOderEnum getByCode(String code) {
        for (UserOderEnum channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        return null;
    }
}