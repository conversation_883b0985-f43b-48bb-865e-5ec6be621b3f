/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.apiconfig.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import cn.com.necloud.admin.common.model.resp.NewBaseDetailResp;
import java.io.Serial;
import java.time.*;

/**
 * api接口配置表详情信息
 *
 * <AUTHOR>
 * @since 2025/07/11 14:13
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "api接口配置表详情信息")
public class ApiInterfaceConfigsDetailResp extends NewBaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @ExcelProperty(value = "主键ID")
    private String businessId;

    /**
     * API名称
     */
    @Schema(description = "API名称")
    @ExcelProperty(value = "API名称")
    private String apiName;

    /**
     * 融合健康档案数据
     */
    @Schema(description = "融合健康档案数据")
    @ExcelProperty(value = "融合健康档案数据")
    private String integratedHealthData;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    @ExcelProperty(value = "备注信息")
    private String remarks;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 供应商API地址
     */
    @Schema(description = "供应商API地址")
    @ExcelProperty(value = "供应商API地址")
    private String supplierApiUrl;

    /**
     * 供应商回调地址
     */
    @Schema(description = "供应商回调地址")
    @ExcelProperty(value = "供应商回调地址")
    private String supplierCallbackUrl;

    /**
     * API密钥，用于接口身份验证
     */
    @Schema(description = "API密钥，用于接口身份验证")
    @ExcelProperty(value = "API密钥，用于接口身份验证")
    private String apiKey;

    /**
     * API密钥，用于接口身份验证
     */
    @Schema(description = "API密钥，用于接口身份验证")
    @ExcelProperty(value = "API密钥，用于接口身份验证")
    private String apiAuthenticationKey;

    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态（0-禁用，1-启用）")
    @ExcelProperty(value = "状态（0-禁用，1-启用）")
    private String userStatus;

    /**
     * 供应商订单编号
     */
    @Schema(description = "供应商订单编号")
    @ExcelProperty(value = "供应商订单编号")
    private Integer supplierOrderId;

    /**
     * 业务唯一标识
     */
    @Schema(description = "业务唯一标识")
    @ExcelProperty(value = "业务唯一标识")
    private String businessIdentifier;

    /**
     * 是否回调标识（0-不回调，1-回调）
     */
    @Schema(description = "是否回调标识（0-不回调，1-回调）")
    @ExcelProperty(value = "是否回调标识（0-不回调，1-回调）")
    private String isCallback;

    /**
     * API请求方法
     */
    @Schema(description = "API请求方法")
    @ExcelProperty(value = "API请求方法")
    private String apiRequestMethod;

    /**
     * 是否启用搜索缓存
     */
    @Schema(description = "是否启用搜索缓存")
    @ExcelProperty(value = "是否启用搜索缓存")
    private String isSearchCache;

    /**
     * 是否生成报告
     */
    @Schema(description = "是否生成报告")
    @ExcelProperty(value = "是否生成报告")
    private String isReportGenerated;

    /**
     * 是否调用报备数据
     */
    @Schema(description = "是否调用报备数据")
    @ExcelProperty(value = "是否调用报备数据")
    private String isCallReportData;
}