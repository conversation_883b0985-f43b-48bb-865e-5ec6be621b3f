/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.auth.service;

import jakarta.servlet.http.HttpServletRequest;
import cn.com.necloud.admin.auth.model.req.LoginReq;
import cn.com.necloud.admin.auth.model.resp.LoginResp;
import cn.com.necloud.admin.auth.model.resp.RouteResp;

import java.util.List;

/**
 * 认证业务接口
 *
 * <AUTHOR>
 * @since 2022/12/21 21:48
 */
public interface AuthService {

    /**
     * 登录
     *
     * @param req     请求参数
     * @param request 请求对象
     * @return 登录响应参数
     */
    LoginResp login(LoginReq req, HttpServletRequest request);

    /**
     * 构建路由树
     *
     * @param userId 用户 ID
     * @return 路由树
     */
    List<RouteResp> buildRouteTree(Long userId);
}
