/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.transfer.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import cn.com.necloud.admin.common.model.resp.NewBaseDetailResp;
import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 转账登记记录表详情信息
 *
 * <AUTHOR>
 * @since 2025/07/15 16:57
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "转账登记记录表详情信息")
public class TransferRecordsDetailResp extends NewBaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @ExcelProperty(value = "主键ID")
    private String businessId;

    /**
     * 资金来源说明
     */
    @Schema(description = "资金来源说明")
    @ExcelProperty(value = "资金来源说明")
    private String fundSourceDescription;

    /**
     * 转入机构名称
     */
    @Schema(description = "转入机构名称")
    @ExcelProperty(value = "转入机构名称")
    private String transferInOrganizationName;

    /**
     * 转出机构名称
     */
    @Schema(description = "转出机构名称")
    @ExcelProperty(value = "转出机构名称")
    private String transferOutOrganizationName;

    /**
     * 付款方名称
     */
    @Schema(description = "付款方名称")
    @ExcelProperty(value = "付款方名称")
    private String payerName;

    /**
     * 付款人手机号码
     */
    @Schema(description = "付款人手机号码")
    @ExcelProperty(value = "付款人手机号码")
    private String payerPhoneNumber;

    /**
     * 转账总金额，单位：元
     */
    @Schema(description = "转账总金额，单位：元")
    @ExcelProperty(value = "转账总金额，单位：元")
    private BigDecimal totalAmount;

    /**
     * 资金用途说明
     */
    @Schema(description = "资金用途说明")
    @ExcelProperty(value = "资金用途说明")
    private String fundUsageDescription;

    /**
     * 转账银行名称
     */
    @Schema(description = "转账银行名称")
    @ExcelProperty(value = "转账银行名称")
    private String transferBankName;

    /**
     * 转账到账时间
     */
    @Schema(description = "转账到账时间")
    @ExcelProperty(value = "转账到账时间")
    private LocalDateTime transferReceiveTime;

    /**
     * 客服人员账号
     */
    @Schema(description = "客服人员账号")
    @ExcelProperty(value = "客服人员账号")
    private String customerServiceStaff;

    /**
     * 支付类型
     */
    @Schema(description = "支付类型")
    @ExcelProperty(value = "支付类型")
    private String paymentType;

    /**
     * 转账凭证附件
     */
    @Schema(description = "转账凭证附件")
    @ExcelProperty(value = "转账凭证附件")
    private String transferVoucherAttachment;

    /**
     * 转账备注信息
     */
    @Schema(description = "转账备注信息")
    @ExcelProperty(value = "转账备注信息")
    private String transferRemark;

    /**
     * 注册时间
     */
    @Schema(description = "注册时间")
    @ExcelProperty(value = "注册时间")
    private LocalDateTime registrationTime;

    /**
     * 转账状态（0-失败，1-成功，2-处理中）
     */
    @Schema(description = "转账状态（0-失败，1-成功，2-处理中）")
    @ExcelProperty(value = "转账状态（0-失败，1-成功，2-处理中）")
    private String transferStatus;

    /**
     * 机构唯一标识
     */
    @Schema(description = "机构唯一标识")
    @ExcelProperty(value = "机构唯一标识")
    private String organizationId;

    /**
     * 转入机构ID
     */
    @Schema(description = "转入机构ID")
    @ExcelProperty(value = "转入机构ID")
    private String transferInOrganizationId;

    /**
     * 地区编号
     */
    @Schema(description = "地区编号")
    @ExcelProperty(value = "地区编号")
    private BigDecimal areaId;
}