/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.servicefreeconfig.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import cn.com.necloud.admin.common.model.resp.NewBaseDetailResp;
import java.io.Serial;
import java.time.*;

/**
 * 服务免费额度配置详情信息
 *
 * <AUTHOR>
 * @since 2025/07/16 11:34
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "服务免费额度配置详情信息")
public class ServiceFreeQuotaConfigsDetailResp extends NewBaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键ID
     */
    @Schema(description = "业务主键ID")
    @ExcelProperty(value = "业务主键ID")
    private String businessId;

    /**
     * 机构唯一标识
     */
    @Schema(description = "机构唯一标识")
    @ExcelProperty(value = "机构唯一标识")
    private String organizationId;

    /**
     * 查询类型（0-组织查询，1-自由查询）
     */
    @Schema(description = "查询类型（0-组织查询，1-自由查询）")
    @ExcelProperty(value = "查询类型（0-组织查询，1-自由查询）")
    private String queryType;

    /**
     * 免费数量
     */
    @Schema(description = "免费数量")
    @ExcelProperty(value = "免费数量")
    private Integer freeCount;

    /**
     * 乐观锁版本号
     */
    @Schema(description = "乐观锁版本号")
    @ExcelProperty(value = "乐观锁版本号")
    private String lockVersion;

    /**
     * 机构名称
     */
    @Schema(description = "机构名称")
    @ExcelProperty(value = "机构名称")
    private String organizationName;
}