/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.account.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 汽修资金账户表实体
 *
 * <AUTHOR>
 * @since 2025/07/15 15:55
 */
@Data
@TableName("hc_garage_fund_accounts")
public class GarageFundAccountsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键，唯一标识记录
     */
    private String businessId;

    /**
     * 是否删除标记（0-未删除，1-已删除）
     */
    private Integer isDeleted;

    /**
     * 用户唯一标识
     */
    private String userId;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private String accountType;

    /**
     * 余额，单位：元
     */
    private BigDecimal balanceAmount;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 乐观锁版本号
     */
    private Integer version;

    /**
     * 机构名称
     */
    private String organizationName;

    /**
     * 地区编号
     */
    private BigDecimal areaId;

    /**
     * 机构唯一标识
     */
    private String organizationId;

    /**
     * 支付密码
     */
    private String payPassword;
}
