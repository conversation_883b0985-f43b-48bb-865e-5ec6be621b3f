/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.brandprice.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;

/**
 * 品牌金额配置表实体
 *
 * <AUTHOR>
 * @since 2025/07/15 17:56
 */
@Data
@TableName("hc_brand_price_config")
public class BrandPriceConfigDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 品牌价格配置主键ID
     */
    private String businessId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 是否有效（0-无效，1-有效）
     */
    private String isValid;

    /**
     * 品牌价格单位为元
     */
    private String brandPrice;

    /**
     * VIP价格，单位：元
     */
    private String vipPrice;

    /**
     * 品牌唯一标识
     */
    private String brandId;

    /**
     * 制造商名称
     */
    private String manufacturerName;

    /**
     * 制造商ID
     */
    private String manufacturerId;
}
