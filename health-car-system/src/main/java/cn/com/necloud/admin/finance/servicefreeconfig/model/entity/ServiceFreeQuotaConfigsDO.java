/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.servicefreeconfig.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;

/**
 * 服务免费额度配置实体
 *
 * <AUTHOR>
 * @since 2025/07/16 11:34
 */
@Data
@TableName("hc_service_free_quota_configs")
public class ServiceFreeQuotaConfigsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键ID
     */
    private String businessId;

    /**
     * 机构唯一标识
     */
    private String organizationId;

    /**
     * 查询类型（0-组织查询，1-自由查询）
     */
    private String queryType;

    /**
     * 免费数量
     */
    private Integer freeCount;

    /**
     * 乐观锁版本号
     */
    private String lockVersion;

    /**
     * 机构名称
     */
    private String organizationName;
}
