/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.user.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 商业 B 端用户实体
 *
 * <AUTHOR>
 * @since 2025/07/10 15:53
 */
@Data
@TableName("hc_business_users")
public class BusinessUsersDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键，客户唯一标识
     */
    private String businessId;

    /**
     * 客户唯一标识
     */
    private String customerId;

    /**
     * 是否可用（0-不可用，1-可用）
     */
    private String isAvailable;

    /**
     * 私钥信息
     */
    private String privateKey;

    /**
     * 公钥信息
     */
    private String publicKey;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * URL地址
     */
    private String urlAddress;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 用户登录凭证
     */
    private String userToken;

    /**
     * 是否已OCR识别
     */
    private String isOcrRecognized;

    /**
     * 更新天数数值
     */
    private String updateDayNumber;

    /**
     * 日期编号，格式为YYYYMMDD
     */
    private String dateNumber;

    /**
     * 用户权限等级
     */
    private Integer userPermissionLevel;

    /**
     * 钱包余额，单位：元
     */
    private BigDecimal walletBalance;

    /**
     * Leap用户ID
     */
    private String leapUserId;

    /**
     * 提醒内容（如生日、纪念日等）
     */
    private String reminderContent;

    /**
     * 提醒手机号码
     */
    private String reminderMobile;

    /**
     * 是否禁止发送短信
     */
    private String isSmsSendingBlocked;

    /**
     * 是否超时推送
     */
    private String isTimeoutPush;
}
