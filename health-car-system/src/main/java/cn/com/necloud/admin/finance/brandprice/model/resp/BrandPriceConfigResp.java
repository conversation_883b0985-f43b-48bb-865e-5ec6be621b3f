/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.brandprice.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import java.io.Serial;
import java.time.*;

/**
 * 品牌金额配置表信息
 *
 * <AUTHOR>
 * @since 2025/07/15 17:56
 */
@Data
@Schema(description = "品牌金额配置表信息")
public class BrandPriceConfigResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 品牌价格配置主键ID
     */
    @Schema(description = "品牌价格配置主键ID")
    private String businessId;

    /**
     * 更新人名称
     */
    @Schema(description = "更新人名称")
    private String updateUser;

    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    private String brandName;

    /**
     * 是否有效（0-无效，1-有效）
     */
    @Schema(description = "是否有效（0-无效，1-有效）")
    private String isValid;

    /**
     * 品牌价格单位为元
     */
    @Schema(description = "品牌价格单位为元")
    private String brandPrice;

    /**
     * VIP价格，单位：元
     */
    @Schema(description = "VIP价格，单位：元")
    private String vipPrice;

    /**
     * 品牌唯一标识
     */
    @Schema(description = "品牌唯一标识")
    private String brandId;

    /**
     * 制造商名称
     */
    @Schema(description = "制造商名称")
    private String manufacturerName;

    /**
     * 制造商ID
     */
    @Schema(description = "制造商ID")
    private String manufacturerId;
}