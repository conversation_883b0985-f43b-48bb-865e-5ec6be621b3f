/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.user.model.query;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;
import java.io.Serial;
import java.io.Serializable;
import java.time.*;

/**
 * 商业 B 端用户查询条件
 *
 * <AUTHOR>
 * @since 2025/07/10 15:53
 */
@Data
@Schema(description = "商业 B 端用户查询条件")
public class BusinessUsersQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 通过用户 id 查询
     *
     */
    @Schema(description = "通过用户 id 查询")
    @Query(type = QueryType.EQ)
    private String customerId;

}