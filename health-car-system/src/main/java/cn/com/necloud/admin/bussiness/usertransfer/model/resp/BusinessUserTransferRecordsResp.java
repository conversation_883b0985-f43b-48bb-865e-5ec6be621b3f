/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.usertransfer.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 商业用户转账记录表信息
 *
 * <AUTHOR>
 * @since 2025/07/10 17:07
 */
@Data
@Schema(description = "商业用户转账记录表信息")
public class BusinessUserTransferRecordsResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键ID
     */
    @Schema(description = "业务主键ID")
    private String businessId;

    /**
     * 更新人名称
     */
    @Schema(description = "更新人名称")
    private String updateUser;

    /**
     * 记录最后更新时间
     */
    @Schema(description = "记录最后更新时间")
    private LocalDateTime updateTime;

    /**
     * 账户余额，单位：分
     */
    @Schema(description = "账户余额，单位：分")
    private BigDecimal accountBalanceInCents;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String remarkText;

    /**
     * 出入库类型（IN-入库，OUT-出库）
     */
    @Schema(description = "出入库类型（IN-入库，OUT-出库）")
    private String inOutType;

    /**
     * 客户唯一标识
     */
    @Schema(description = "客户唯一标识")
    private String customerId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String customerName;
}