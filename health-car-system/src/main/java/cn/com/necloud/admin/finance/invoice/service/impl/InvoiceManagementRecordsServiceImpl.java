/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.invoice.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.finance.invoice.mapper.InvoiceManagementRecordsMapper;
import cn.com.necloud.admin.finance.invoice.model.entity.InvoiceManagementRecordsDO;
import cn.com.necloud.admin.finance.invoice.model.query.InvoiceManagementRecordsQuery;
import cn.com.necloud.admin.finance.invoice.model.req.InvoiceManagementRecordsReq;
import cn.com.necloud.admin.finance.invoice.model.resp.InvoiceManagementRecordsDetailResp;
import cn.com.necloud.admin.finance.invoice.model.resp.InvoiceManagementRecordsResp;
import cn.com.necloud.admin.finance.invoice.service.InvoiceManagementRecordsService;

/**
 * 发票管理业务实现
 *
 * <AUTHOR>
 * @since 2025/07/16 10:54
 */
@Service
@RequiredArgsConstructor
public class InvoiceManagementRecordsServiceImpl extends BaseServiceImpl<InvoiceManagementRecordsMapper, InvoiceManagementRecordsDO, InvoiceManagementRecordsResp, InvoiceManagementRecordsDetailResp, InvoiceManagementRecordsQuery, InvoiceManagementRecordsReq> implements InvoiceManagementRecordsService {}