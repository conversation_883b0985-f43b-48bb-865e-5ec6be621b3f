/*
 * Copyright (c) 2022-present <PERSON>7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ParameterEnum {

    /**
     * 查询成功
     */
    PAY_FAIL("0", "支付失败"),

    /**
     * 查询失败
     */
    PAY_SUCCESS("1", "支付成功"), VIN_ANALYSIS("2", "vin解析"), ADDITIONAL_QUERY("3", "附加查询"), PENGHAI("4", "鹏海");

    private final String code;
    private final String desc;
}
