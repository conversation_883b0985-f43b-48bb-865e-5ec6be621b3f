/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.quote.service;

import top.continew.starter.extension.crud.service.BaseService;
import cn.com.necloud.admin.finance.quote.model.query.ServiceQuoteConfigQuery;
import cn.com.necloud.admin.finance.quote.model.req.ServiceQuoteConfigReq;
import cn.com.necloud.admin.finance.quote.model.resp.ServiceQuoteConfigDetailResp;
import cn.com.necloud.admin.finance.quote.model.resp.ServiceQuoteConfigResp;

/**
 * 服务报价配置表业务接口
 *
 * <AUTHOR>
 * @since 2025/07/15 17:40
 */
public interface ServiceQuoteConfigService extends BaseService<ServiceQuoteConfigResp, ServiceQuoteConfigDetailResp, ServiceQuoteConfigQuery, ServiceQuoteConfigReq> {}