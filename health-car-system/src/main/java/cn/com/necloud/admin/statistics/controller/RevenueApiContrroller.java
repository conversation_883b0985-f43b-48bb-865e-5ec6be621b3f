/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.controller;

import cn.com.necloud.admin.statistics.model.query.RevenueExpensesQuery;
import cn.com.necloud.admin.statistics.model.util.PageResult;
import cn.com.necloud.admin.statistics.model.util.PageRevenue;
import cn.com.necloud.admin.statistics.service.RevenueExpensesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.starter.extension.crud.model.query.PageQuery;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-17
 * @Description:
 * @Version: 1.8
 */

@Tag(name = "统计管理模块 ")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/statists")
public class RevenueApiContrroller {
    private final RevenueExpensesService revenueExpensesService;

    /***
     * 接口统计
     */
    @Operation(summary = "分页查询收支管理", description = "分页查询收支管理")
    @GetMapping("/reven")
    public PageResult revenueExpenses(RevenueExpensesQuery query, @Validated PageQuery pageQuery) {
        return revenueExpensesService.page(query, pageQuery);
    }

    /***
     * 程序统计
     */
    @Operation(summary = "分页查询收支管理", description = "分页查询收支管理")
    @GetMapping("/program")
    public PageRevenue program(RevenueExpensesQuery query, @Validated PageQuery pageQuery) {
        return revenueExpensesService.pageProgram(query, pageQuery);
    }
}
