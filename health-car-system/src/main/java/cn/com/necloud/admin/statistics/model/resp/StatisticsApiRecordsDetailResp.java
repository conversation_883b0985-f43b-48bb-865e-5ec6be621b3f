/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-16
 * @Description: 统计接口管理实体对象
 * @Version: 1.8
 */

@Data
@ExcelIgnoreUnannotated
@Schema(description = "api接口管理信息")
public class StatisticsApiRecordsDetailResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户
     */
    @Schema(description = "")
    private String userName;
    /**
     * 调用接口数量
     */
    @Schema(description = "")
    private Integer apiCount;
    /**
     * 推送成功数量
     */
    @Schema(description = "")
    private Integer pushSuccessCount;
    /**
     * 推送失败数量
     */
    @Schema(description = "")
    private Integer pushFailCount;
    /**
     * 推送成功占比
     */
    @Schema(description = "")
    private String pushSuccessRate;
    /**
     * 推送失败占比
     */
    @Schema(description = "")
    private String pushFailRate;

    /**
     * 第三方回调超时数量
     */
    @Schema(description = "")
    private Integer thirdPartyCallbackTimeoutCount;

    /**
     * 第三方回调超时占比
     */
    @Schema(description = "")
    private String thirdPartyCallbackTimeoutRate;
    /**
     * 第三方回调成功数量
     */
    @Schema(description = "")
    private Integer thirdPartyCallbackSuccessCount;
    /**
     * 第三方回调成功占比
     */
    @Schema(description = "")
    private String thirdPartyCallbackSuccessRate;
    /**
     * 健康档案有数据数量
     */
    @Schema(description = "")
    private Integer healthRecordHasCount;
    /**
     * 健康档案无数据数量
     */
    @Schema(description = "")
    private Integer healthRecordNoCount;
    /**
     * 健康档案无数据占比
     */
    @Schema(description = "")
    private String healthRecordNoRate;

}
