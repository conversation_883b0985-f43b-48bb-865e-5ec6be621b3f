/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.apimonitor.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.bussiness.apimonitor.mapper.ApiMonitorRecordsMapper;
import cn.com.necloud.admin.bussiness.apimonitor.model.entity.ApiMonitorRecordsDO;
import cn.com.necloud.admin.bussiness.apimonitor.model.query.ApiMonitorRecordsQuery;
import cn.com.necloud.admin.bussiness.apimonitor.model.req.ApiMonitorRecordsReq;
import cn.com.necloud.admin.bussiness.apimonitor.model.resp.ApiMonitorRecordsDetailResp;
import cn.com.necloud.admin.bussiness.apimonitor.model.resp.ApiMonitorRecordsResp;
import cn.com.necloud.admin.bussiness.apimonitor.service.ApiMonitorRecordsService;

/**
 * api监听记录业务实现
 *
 * <AUTHOR>
 * @since 2025/07/15 10:17
 */
@Service
@RequiredArgsConstructor
public class ApiMonitorRecordsServiceImpl extends BaseServiceImpl<ApiMonitorRecordsMapper, ApiMonitorRecordsDO, ApiMonitorRecordsResp, ApiMonitorRecordsDetailResp, ApiMonitorRecordsQuery, ApiMonitorRecordsReq> implements ApiMonitorRecordsService {}