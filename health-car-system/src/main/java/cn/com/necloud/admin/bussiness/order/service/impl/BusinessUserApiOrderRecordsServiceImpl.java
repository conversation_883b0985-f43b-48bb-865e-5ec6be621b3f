/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.order.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.bussiness.order.mapper.BusinessUserApiOrderRecordsMapper;
import cn.com.necloud.admin.bussiness.order.model.entity.BusinessUserApiOrderRecordsDO;
import cn.com.necloud.admin.bussiness.order.model.query.BusinessUserApiOrderRecordsQuery;
import cn.com.necloud.admin.bussiness.order.model.req.BusinessUserApiOrderRecordsReq;
import cn.com.necloud.admin.bussiness.order.model.resp.BusinessUserApiOrderRecordsDetailResp;
import cn.com.necloud.admin.bussiness.order.model.resp.BusinessUserApiOrderRecordsResp;
import cn.com.necloud.admin.bussiness.order.service.BusinessUserApiOrderRecordsService;

/**
 * 商业用户 api 调用订单表业务实现
 *
 * <AUTHOR>
 * @since 2025/07/10 19:06
 */
@Service
@RequiredArgsConstructor
public class BusinessUserApiOrderRecordsServiceImpl extends BaseServiceImpl<BusinessUserApiOrderRecordsMapper, BusinessUserApiOrderRecordsDO, BusinessUserApiOrderRecordsResp, BusinessUserApiOrderRecordsDetailResp, BusinessUserApiOrderRecordsQuery, BusinessUserApiOrderRecordsReq> implements BusinessUserApiOrderRecordsService {}