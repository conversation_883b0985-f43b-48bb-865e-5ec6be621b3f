/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.account.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.finance.account.mapper.GarageFundAccountsMapper;
import cn.com.necloud.admin.finance.account.model.entity.GarageFundAccountsDO;
import cn.com.necloud.admin.finance.account.model.query.GarageFundAccountsQuery;
import cn.com.necloud.admin.finance.account.model.req.GarageFundAccountsReq;
import cn.com.necloud.admin.finance.account.model.resp.GarageFundAccountsDetailResp;
import cn.com.necloud.admin.finance.account.model.resp.GarageFundAccountsResp;
import cn.com.necloud.admin.finance.account.service.GarageFundAccountsService;

/**
 * 汽修资金账户表业务实现
 *
 * <AUTHOR>
 * @since 2025/07/15 15:55
 */
@Service
@RequiredArgsConstructor
public class GarageFundAccountsServiceImpl extends BaseServiceImpl<GarageFundAccountsMapper, GarageFundAccountsDO, GarageFundAccountsResp, GarageFundAccountsDetailResp, GarageFundAccountsQuery, GarageFundAccountsReq> implements GarageFundAccountsService {}