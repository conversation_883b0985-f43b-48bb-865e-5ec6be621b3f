/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.organizational.model.query;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

import java.io.Serial;
import java.io.Serializable;

/**
 * 汽修机构表查询条件
 *
 * <AUTHOR>
 * @since 2025/07/10 17:32
 */
@Data
@Schema(description = "汽修机构表查询条件")
public class OrganizationQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /*
     *机构名称
     */
    @Schema(description = "机构名称", example = "example")
    @Query(type = QueryType.LIKE)
    private String organizationName;
    /*
     *统一社会信用代码
     */
    @Schema(description = "统一社会信用代码", example = "example")
    @Query(type = QueryType.LIKE)
    private String organizationCode;

    //所在地区
    @Schema(description = "所在地区", example = "example")
    @Query(type = QueryType.LIKE_RIGHT)
    private String areaId;
    /*
     *审核状态
     */
    @Schema(description = "审核状态", example = "example")
    @Query(type = QueryType.EQ)
    private String isApproved;
    /*
     *联系电话
     */
    //todo   解密
    @Schema(description = "联系电话", example = "example")
    @Query(type = QueryType.EQ)
    private String phone;

}