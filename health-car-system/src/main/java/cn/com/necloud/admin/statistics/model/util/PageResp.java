/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.model.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;

import java.util.List;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-17
 * @Description:
 * @Version: 1.8
 */

@Data
public class PageResp<T> {
    private Long current;
    private Long size;
    private Long total;
    private List<T> records;
    private T totalRow; // 新增合计行字段

    public static <T> PageResp<T> builds(IPage<T> page) {
        PageResp<T> pageResp = new PageResp<>();
        pageResp.setCurrent(page.getCurrent());
        pageResp.setSize(page.getSize());
        pageResp.setTotal(page.getTotal());
        pageResp.setRecords(page.getRecords());
        return pageResp;
    }
}
