/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.datacache.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;

/**
 * 汽车数据缓存表实体
 *
 * <AUTHOR>
 * @since 2025/07/11 09:43
 */
@Data
@TableName("hc_car_data_cache_records")
public class CarDataCacheRecordsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String businessId;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 客户唯一标识
     */
    private String customerId;

    /**
     * 车辆识别码
     */
    private String vehicleIdentificationNumber;

    /**
     * 查询类型
     */
    private String queryType;

    /**
     * 数据内容
     */
    private String dataContent;

    /**
     * 数据类型标识
     */
    private String dataTypeIdentifier;
}
