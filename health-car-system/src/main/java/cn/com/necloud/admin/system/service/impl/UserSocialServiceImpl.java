/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import me.zhyd.oauth.model.AuthUser;
import org.springframework.stereotype.Service;
import cn.com.necloud.admin.system.enums.SocialSourceEnum;
import cn.com.necloud.admin.system.mapper.user.UserSocialMapper;
import cn.com.necloud.admin.system.model.entity.user.UserSocialDO;
import cn.com.necloud.admin.system.service.UserSocialService;
import top.continew.starter.core.validation.CheckUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户社会化关联业务实现
 *
 * <AUTHOR>
 * @since 2023/10/11 22:10
 */
@Service
@RequiredArgsConstructor
public class UserSocialServiceImpl implements UserSocialService {

    private final UserSocialMapper baseMapper;

    @Override
    public UserSocialDO getBySourceAndOpenId(String source, String openId) {
        return baseMapper.selectBySourceAndOpenId(source, openId);
    }

    @Override
    public void saveOrUpdate(UserSocialDO userSocial) {
        if (userSocial.getCreateTime() == null) {
            baseMapper.insert(userSocial);
        } else {
            baseMapper.lambdaUpdate()
                .set(UserSocialDO::getMetaJson, userSocial.getMetaJson())
                .set(UserSocialDO::getLastLoginTime, userSocial.getLastLoginTime())
                .eq(UserSocialDO::getSource, userSocial.getSource())
                .eq(UserSocialDO::getOpenId, userSocial.getOpenId())
                .update();
        }
    }

    @Override
    public List<UserSocialDO> listByUserId(Long userId) {
        return baseMapper.lambdaQuery().eq(UserSocialDO::getUserId, userId).list();
    }

    @Override
    public void bind(AuthUser authUser, Long userId) {
        String source = authUser.getSource();
        String openId = authUser.getUuid();
        List<UserSocialDO> userSocialList = this.listByUserId(userId);
        Set<String> boundSocialSet = userSocialList.stream().map(UserSocialDO::getSource).collect(Collectors.toSet());
        String description = SocialSourceEnum.valueOf(source).getDescription();
        CheckUtils.throwIf(boundSocialSet.contains(source), "您已经绑定过了 [{}] 平台，请先解绑", description);
        UserSocialDO userSocial = this.getBySourceAndOpenId(source, openId);
        CheckUtils.throwIfNotNull(userSocial, "[{}] 平台账号 [{}] 已被其他用户绑定", description, authUser.getUsername());
        userSocial = new UserSocialDO();
        userSocial.setUserId(userId);
        userSocial.setSource(source);
        userSocial.setOpenId(openId);
        userSocial.setMetaJson(JSONUtil.toJsonStr(authUser));
        userSocial.setLastLoginTime(LocalDateTime.now());
        baseMapper.insert(userSocial);
    }

    @Override
    public void deleteBySourceAndUserId(String source, Long userId) {
        baseMapper.lambdaUpdate().eq(UserSocialDO::getSource, source).eq(UserSocialDO::getUserId, userId).remove();
    }

    @Override
    public void deleteByUserIds(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return;
        }
        baseMapper.lambdaUpdate().in(UserSocialDO::getUserId, userIds).remove();
    }
}
