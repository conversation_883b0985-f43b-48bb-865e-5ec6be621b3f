/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.groupservice.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;

/**
 * 用户服务报价配置表实体
 *
 * <AUTHOR>
 * @since 2025/07/16 11:19
 */
@Data
@TableName("hc_user_service_quote_configs")
public class UserServiceQuoteConfigsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键，客户配置唯一标识
     */
    private String businessId;

    /**
     * 客户分组名称
     */
    private String customerGroupName;

    /**
     * 服务价格，单位：元
     */
    private String servicePrice;

    /**
     * 查询类型
     */
    private String queryType;

    /**
     * 渠道类型
     */
    private String channelType;
}
