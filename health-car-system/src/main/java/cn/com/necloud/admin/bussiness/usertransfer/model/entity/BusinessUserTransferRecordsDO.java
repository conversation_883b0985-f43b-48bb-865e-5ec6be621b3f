/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.usertransfer.model.entity;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import cn.com.necloud.admin.common.model.entity.NewBaseDO;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 商业用户转账记录表实体
 *
 * <AUTHOR>
 * @since 2025/07/10 17:07
 */
@Data
@TableName("hc_business_user_transfer_records")
public class BusinessUserTransferRecordsDO extends NewBaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键ID
     */
    private String businessId;

    /**
     * 账户余额，单位：分
     */
    private BigDecimal accountBalanceInCents;

    /**
     * 备注信息
     */
    private String remarkText;

    /**
     * 出入库类型（IN-入库，OUT-出库）
     */
    private String inOutType;

    /**
     * 客户唯一标识
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;
}
