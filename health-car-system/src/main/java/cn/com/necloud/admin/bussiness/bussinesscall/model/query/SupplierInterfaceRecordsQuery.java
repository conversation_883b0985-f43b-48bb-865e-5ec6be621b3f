/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.bussinesscall.model.query;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;
import java.io.Serial;
import java.io.Serializable;
import java.time.*;

/**
 * 厂商接口调用记录表查询条件
 *
 * <AUTHOR>
 * @since 2025/07/11 14:36
 */
@Data
@Schema(description = "厂商接口调用记录表查询条件")
public class SupplierInterfaceRecordsQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 通过订单号查询
     */
    @Schema(description = "订单号")
    @Query(type = QueryType.EQ)
    private String orderNumber;

    /**
     * 通过第三方订单号进行查询
     */
    @Schema(description = "第三方订单号")
    @Query(type = QueryType.EQ)
    private String supplierOrderNumber;

    /**
     * 通过接口名称进行查询
     */
    @Schema(description = "接口名称")
    @Query(type = QueryType.EQ)
    private String apiName;

    /**
     * 通过车架号进行查询
     */
    @Schema(description = "车架号")
    @Query(type = QueryType.EQ)
    private String vin;

}