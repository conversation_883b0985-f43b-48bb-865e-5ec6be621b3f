/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.controller;

import cn.com.necloud.admin.statistics.model.query.InterfaceQuery;
import cn.com.necloud.admin.statistics.model.resp.StatisticsApiRecordsDetailResp;
import cn.com.necloud.admin.statistics.service.StatisticsApiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import top.continew.starter.extension.crud.model.query.PageQuery;
import cn.com.necloud.admin.statistics.model.util.PageResp;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-16
 * @Description: 统计api数据与收入支出管理 API
 * @Version: 1.8
 */

@Tag(name = "统计管理模块 ")
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/statists")
public class StatisticsApiIncomeExpensesController {

    private final StatisticsApiService statisticsApiService;

    /***
     * 接口统计
     */
    @Operation(summary = "分页查询接口统计管理", description = "分页查询接口统计管理")
    @GetMapping("/api")
    public PageResp<StatisticsApiRecordsDetailResp> getStatisticsPage(@Valid InterfaceQuery query,
                                                                      @Valid PageQuery pageQuery) {
        PageResp<StatisticsApiRecordsDetailResp> page = statisticsApiService.page(query, pageQuery);
        return page;
    }

}
