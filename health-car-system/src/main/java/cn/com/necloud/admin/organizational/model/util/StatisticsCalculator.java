/*
 * Copyright (c) 2022-present <PERSON><PERSON>c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.organizational.model.util;

import cn.com.necloud.admin.statistics.model.resp.StatisticsApiRecordsDetailResp;

import java.util.List;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-17
 * @Description: 分页计算
 * @Version: 1.8
 */

public class StatisticsCalculator {
    public static StatisticsApiRecordsDetailResp calculateTotal(List<StatisticsApiRecordsDetailResp> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }

        StatisticsApiRecordsDetailResp total = new StatisticsApiRecordsDetailResp();
        total.setUserName("合计");

        int totalApiCalls = list.stream().mapToInt(StatisticsApiRecordsDetailResp::getApiCount).sum();
        int totalSuccess = list.stream().mapToInt(StatisticsApiRecordsDetailResp::getPushSuccessCount).sum();
        int totalFailure = list.stream().mapToInt(StatisticsApiRecordsDetailResp::getPushFailCount).sum();
        int totalTimeout = list.stream()
            .mapToInt(StatisticsApiRecordsDetailResp::getThirdPartyCallbackTimeoutCount)
            .sum();
        int totalCallbackSuccess = list.stream()
            .mapToInt(StatisticsApiRecordsDetailResp::getThirdPartyCallbackSuccessCount)
            .sum();
        int totalValidData = list.stream().mapToInt(StatisticsApiRecordsDetailResp::getHealthRecordHasCount).sum();

        total.setApiCount(totalApiCalls);
        total.setPushSuccessCount(totalSuccess);
        total.setPushFailCount(totalFailure);
        total.setThirdPartyCallbackTimeoutCount(totalTimeout);
        total.setThirdPartyCallbackSuccessCount(totalCallbackSuccess);
        total.setHealthRecordHasCount(totalValidData);

        // 计算百分比
        total.setPushSuccessRate(String.valueOf(totalApiCalls == 0 ? 0 : (double)totalSuccess / totalApiCalls * 100));
        total.setPushFailRate(String.valueOf(totalApiCalls == 0 ? 0 : (double)totalFailure / totalApiCalls * 100));
        total.setThirdPartyCallbackTimeoutRate(String.valueOf(totalApiCalls == 0
            ? 0
            : (double)totalTimeout / totalApiCalls * 100));
        total.setThirdPartyCallbackSuccessRate(String.valueOf(totalApiCalls == 0
            ? 0
            : (double)totalCallbackSuccess / totalApiCalls * 100));
        total.setHealthRecordNoRate(String.valueOf(totalApiCalls == 0
            ? 0
            : (double)totalValidData / totalApiCalls * 100));

        return total;
    }
}
