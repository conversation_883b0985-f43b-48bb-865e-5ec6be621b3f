/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.invoice.model.query;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;
import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.util.Date;
import java.util.List;

/**
 * 发票管理查询条件
 *
 * <AUTHOR>
 * @since 2025/07/16 10:54
 */
@Data
@Schema(description = "发票管理查询条件")
public class InvoiceManagementRecordsQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 根据用户名进行查询
     */
    @Schema(description = "根据用户名进行查询")
    @Query(type = QueryType.LIKE)
    private String userName;

    /**
     * 根据发票状态进行查询
     */
    @Schema(description = "根据发票状态进行查询")
    @Query(type = QueryType.EQ)
    private String invoiceStatus;

    /**
     * 根据发票类型进行查询
     */
    @Schema(description = "根据发票类型进行查询")
    @Query(type = QueryType.EQ)
    private String invoiceType;

    /**
     * 根据付款时间段进行查询
     */
    @Schema(description = "根据付款时间段进行查询")
    @Query(type = QueryType.BETWEEN)
    private List<Date> paymentTime;

}