/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.servicefreeconfig.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.finance.servicefreeconfig.mapper.ServiceFreeQuotaConfigsMapper;
import cn.com.necloud.admin.finance.servicefreeconfig.model.entity.ServiceFreeQuotaConfigsDO;
import cn.com.necloud.admin.finance.servicefreeconfig.model.query.ServiceFreeQuotaConfigsQuery;
import cn.com.necloud.admin.finance.servicefreeconfig.model.req.ServiceFreeQuotaConfigsReq;
import cn.com.necloud.admin.finance.servicefreeconfig.model.resp.ServiceFreeQuotaConfigsDetailResp;
import cn.com.necloud.admin.finance.servicefreeconfig.model.resp.ServiceFreeQuotaConfigsResp;
import cn.com.necloud.admin.finance.servicefreeconfig.service.ServiceFreeQuotaConfigsService;

/**
 * 服务免费额度配置业务实现
 *
 * <AUTHOR>
 * @since 2025/07/16 11:34
 */
@Service
@RequiredArgsConstructor
public class ServiceFreeQuotaConfigsServiceImpl extends BaseServiceImpl<ServiceFreeQuotaConfigsMapper, ServiceFreeQuotaConfigsDO, ServiceFreeQuotaConfigsResp, ServiceFreeQuotaConfigsDetailResp, ServiceFreeQuotaConfigsQuery, ServiceFreeQuotaConfigsReq> implements ServiceFreeQuotaConfigsService {}