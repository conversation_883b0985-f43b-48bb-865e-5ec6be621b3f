/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-16
 * @Description:
 * @Version: 1.8
 */
@Data
@Schema(description = "接口统计及收支统计")
public class InterfaceQuery {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     *
     */

    /**
     * 接口类型
     */
    @Schema(description = "接口类型", example = "接口类型")
    private String apiName;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期", example = "开始日期")
    private String cratetime;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期", example = "结束日期")
    private String endtime;
}
