/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.order.model.query;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;
import java.io.Serial;
import java.io.Serializable;
import java.time.*;

/**
 * 商业用户 api 调用订单表查询条件
 *
 * <AUTHOR>
 * @since 2025/07/10 19:06
 */
@Data
@Schema(description = "商业用户 api 调用订单表查询条件")
public class BusinessUserApiOrderRecordsQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 根据创建时间范围进行筛选
     */
    @Schema(description = "根据创建时间范围进行筛选")
    @Query(type = QueryType.BETWEEN)
    private LocalDateTime[] createTime;

    /**
     * 根据用户 id
     */
    @Schema(description = "根据用户 id ")
    @Query(type = QueryType.EQ)
    private String customerId;

    /**
     * 根据订单编号进行查询
     */
    @Schema(description = "根据订单编号进行查询")
    @Query(type = QueryType.EQ)
    private String orderNumber;

    /**
     * 根据车架号码进行查询
     */
    @Schema(description = "根据车架号码进行查询")
    @Query(type = QueryType.EQ)
    private String vin;

    /**
     * 根据第三方订单号
     */
    @Schema(description = "根据第三方订单号")
    @Query(type = QueryType.EQ)
    private String orderBusinessId;

    /**
     * 根据接口名 进行查询
     */
    @Schema(description = "根据接口名 进行查询")
    @Query(type = QueryType.EQ)
    private String apiName;

}