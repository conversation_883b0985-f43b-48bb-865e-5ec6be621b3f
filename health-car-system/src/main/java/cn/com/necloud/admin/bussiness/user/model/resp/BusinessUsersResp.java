/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.user.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import cn.com.necloud.admin.common.model.resp.NewBaseResp;
import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

/**
 * 商业 B 端用户信息
 *
 * <AUTHOR>
 * @since 2025/07/10 15:53
 */
@Data
@Schema(description = "商业 B 端用户信息")
public class BusinessUsersResp extends NewBaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键，客户唯一标识
     */
    @Schema(description = "业务主键，客户唯一标识")
    private String businessId;

    /**
     * 更新人名称
     */
    @Schema(description = "更新人名称")
    private String updateUser;

    /**
     * 记录更新时间
     */
    @Schema(description = "记录更新时间")
    private LocalDateTime updateTime;

    /**
     * 客户唯一标识
     */
    @Schema(description = "客户唯一标识")
    private String customerId;

    /**
     * 是否可用（0-不可用，1-可用）
     */
    @Schema(description = "是否可用（0-不可用，1-可用）")
    private String isAvailable;

    /**
     * 私钥信息
     */
    @Schema(description = "私钥信息")
    private String privateKey;

    /**
     * 公钥信息
     */
    @Schema(description = "公钥信息")
    private String publicKey;

    /**
     * IP地址
     */
    @Schema(description = "IP地址")
    private String ipAddress;

    /**
     * URL地址
     */
    @Schema(description = "URL地址")
    private String urlAddress;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 用户登录凭证
     */
    @Schema(description = "用户登录凭证")
    private String userToken;

    /**
     * 是否已OCR识别
     */
    @Schema(description = "是否已OCR识别")
    private String isOcrRecognized;

    /**
     * 更新天数数值
     */
    @Schema(description = "更新天数数值")
    private String updateDayNumber;

    /**
     * 日期编号，格式为YYYYMMDD
     */
    @Schema(description = "日期编号，格式为YYYYMMDD")
    private String dateNumber;

    /**
     * 用户权限等级
     */
    @Schema(description = "用户权限等级")
    private Integer userPermissionLevel;

    /**
     * 钱包余额，单位：元
     */
    @Schema(description = "钱包余额，单位：元")
    private BigDecimal walletBalance;

    /**
     * Leap用户ID
     */
    @Schema(description = "Leap用户ID")
    private String leapUserId;

    /**
     * 提醒内容（如生日、纪念日等）
     */
    @Schema(description = "提醒内容（如生日、纪念日等）")
    private String reminderContent;

    /**
     * 提醒手机号码
     */
    @Schema(description = "提醒手机号码")
    private String reminderMobile;

    /**
     * 是否禁止发送短信
     */
    @Schema(description = "是否禁止发送短信")
    private String isSmsSendingBlocked;

    /**
     * 是否超时推送
     */
    @Schema(description = "是否超时推送")
    private String isTimeoutPush;
}