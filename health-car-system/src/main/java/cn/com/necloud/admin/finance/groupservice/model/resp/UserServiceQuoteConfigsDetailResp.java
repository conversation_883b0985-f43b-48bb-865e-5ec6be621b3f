/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.groupservice.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import cn.com.necloud.admin.common.model.resp.NewBaseDetailResp;
import java.io.Serial;
import java.time.*;

/**
 * 用户服务报价配置表详情信息
 *
 * <AUTHOR>
 * @since 2025/07/16 11:19
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "用户服务报价配置表详情信息")
public class UserServiceQuoteConfigsDetailResp extends NewBaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业务主键，客户配置唯一标识
     */
    @Schema(description = "业务主键，客户配置唯一标识")
    @ExcelProperty(value = "业务主键，客户配置唯一标识")
    private String businessId;

    /**
     * 客户分组名称
     */
    @Schema(description = "客户分组名称")
    @ExcelProperty(value = "客户分组名称")
    private String customerGroupName;

    /**
     * 服务价格，单位：元
     */
    @Schema(description = "服务价格，单位：元")
    @ExcelProperty(value = "服务价格，单位：元")
    private String servicePrice;

    /**
     * 查询类型
     */
    @Schema(description = "查询类型")
    @ExcelProperty(value = "查询类型")
    private String queryType;

    /**
     * 渠道类型
     */
    @Schema(description = "渠道类型")
    @ExcelProperty(value = "渠道类型")
    private String channelType;
}