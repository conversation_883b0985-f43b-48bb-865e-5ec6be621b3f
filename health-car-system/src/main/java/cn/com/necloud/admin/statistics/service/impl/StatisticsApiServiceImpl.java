/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.statistics.service.impl;

import cn.com.necloud.admin.bussiness.order.mapper.BusinessUserApiOrderRecordsMapper;
import cn.com.necloud.admin.bussiness.order.model.entity.BusinessUserApiOrderRecordsDO;
import cn.com.necloud.admin.organizational.model.util.StatisticsCalculator;
import cn.com.necloud.admin.statistics.model.query.InterfaceQuery;
import cn.com.necloud.admin.statistics.model.resp.StatisticsApiRecordsDetailResp;
import cn.com.necloud.admin.statistics.service.StatisticsApiService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import top.continew.starter.data.mp.util.QueryWrapperHelper;
import top.continew.starter.extension.crud.model.query.PageQuery;
import cn.com.necloud.admin.statistics.model.util.PageResp;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-16
 * @Description:
 * @Version: 1.8
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsApiServiceImpl implements StatisticsApiService {

    private final BusinessUserApiOrderRecordsMapper businessUserApiOrderRecordsMapper;

    /**
     * 分页查询接口统计管理
     *
     * @param query
     * @param pageQuery
     * @return
     */
    @Override
    public PageResp<StatisticsApiRecordsDetailResp> page(InterfaceQuery query, PageQuery pageQuery) {
        // 1. 构建查询条件
        QueryWrapper<BusinessUserApiOrderRecordsDO> queryWrapper = this.buildQueryWrapper(query);
        QueryWrapperHelper.sort(queryWrapper, pageQuery.getSort());
        // 2. 执行分页查询
        IPage<StatisticsApiRecordsDetailResp> page = statisticsApiIPage(pageQuery, queryWrapper);
        // 3. 计算合计行
        StatisticsApiRecordsDetailResp totalRow = StatisticsCalculator.calculateTotal(page.getRecords());

        // 4. 创建扩展的响应对象
        PageResp<StatisticsApiRecordsDetailResp> response = PageResp.builds(page);

        // 5. 添加合计行到响应中
        response.setTotalRow(totalRow);

        return response;
    }

    @Cacheable(value = "interface_statistics", key = "#query.hashCode()", unless = "#result.size == 0")
    public IPage<StatisticsApiRecordsDetailResp> statisticsApiIPage(PageQuery pageQuery,
                                                                    QueryWrapper<BusinessUserApiOrderRecordsDO> queryWrapper) {
        return businessUserApiOrderRecordsMapper.selectStatisticsPage(new Page<>(pageQuery.getPage(), pageQuery
            .getSize()), queryWrapper);
    }

    private QueryWrapper<BusinessUserApiOrderRecordsDO> buildQueryWrapper(InterfaceQuery query) {
        QueryWrapper<BusinessUserApiOrderRecordsDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(StringUtil.isNotEmpty(query.getApiName()), BusinessUserApiOrderRecordsDO::getApiName, query
                .getApiName())
            .ge(StringUtil.isNotEmpty(query.getCratetime()), BusinessUserApiOrderRecordsDO::getCreateTime, query
                .getCratetime())
            .le(StringUtil.isNotEmpty(query.getEndtime()), BusinessUserApiOrderRecordsDO::getCreateTime, query
                .getEndtime())
            .groupBy(BusinessUserApiOrderRecordsDO::getCustomerId);
        return queryWrapper;
    }
}
