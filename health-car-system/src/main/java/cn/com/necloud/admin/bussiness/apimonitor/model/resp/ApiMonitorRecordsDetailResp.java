/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.bussiness.apimonitor.model.resp;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import cn.com.necloud.admin.common.model.resp.NewBaseDetailResp;
import java.io.Serial;
import java.time.*;

/**
 * api监听记录详情信息
 *
 * <AUTHOR>
 * @since 2025/07/15 10:17
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "api监听记录详情信息")
public class ApiMonitorRecordsDetailResp extends NewBaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @ExcelProperty(value = "主键ID")
    private String businessId;

    /**
     * 消息数量
     */
    @Schema(description = "消息数量")
    @ExcelProperty(value = "消息数量")
    private Integer messageCount;

    /**
     * 序号
     */
    @Schema(description = "序号")
    @ExcelProperty(value = "序号")
    private Integer sequenceNumber;

    /**
     * 提醒次数
     */
    @Schema(description = "提醒次数")
    @ExcelProperty(value = "提醒次数")
    private Integer remindCount;

    /**
     * 提醒手机号码
     */
    @Schema(description = "提醒手机号码")
    @ExcelProperty(value = "提醒手机号码")
    private String reminderMobile;

    /**
     * 状态（0-禁用，1-启用）
     */
    @Schema(description = "状态（0-禁用，1-启用）")
    @ExcelProperty(value = "状态（0-禁用，1-启用）")
    private String isActive;

    /**
     * API名称
     */
    @Schema(description = "API名称")
    @ExcelProperty(value = "API名称")
    private String apiName;

    /**
     * 最后操作时间
     */
    @Schema(description = "最后操作时间")
    @ExcelProperty(value = "最后操作时间")
    private LocalDateTime lastOperationTime;
}