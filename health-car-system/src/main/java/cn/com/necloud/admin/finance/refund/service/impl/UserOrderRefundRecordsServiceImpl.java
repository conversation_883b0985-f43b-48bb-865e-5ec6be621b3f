/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.finance.refund.service.impl;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.starter.extension.crud.service.BaseServiceImpl;
import cn.com.necloud.admin.finance.refund.mapper.UserOrderRefundRecordsMapper;
import cn.com.necloud.admin.finance.refund.model.entity.UserOrderRefundRecordsDO;
import cn.com.necloud.admin.finance.refund.model.query.UserOrderRefundRecordsQuery;
import cn.com.necloud.admin.finance.refund.model.req.UserOrderRefundRecordsReq;
import cn.com.necloud.admin.finance.refund.model.resp.UserOrderRefundRecordsDetailResp;
import cn.com.necloud.admin.finance.refund.model.resp.UserOrderRefundRecordsResp;
import cn.com.necloud.admin.finance.refund.service.UserOrderRefundRecordsService;

/**
 * 用户订单退款记录业务实现
 *
 * <AUTHOR>
 * @since 2025/07/16 11:49
 */
@Service
@RequiredArgsConstructor
public class UserOrderRefundRecordsServiceImpl extends BaseServiceImpl<UserOrderRefundRecordsMapper, UserOrderRefundRecordsDO, UserOrderRefundRecordsResp, UserOrderRefundRecordsDetailResp, UserOrderRefundRecordsQuery, UserOrderRefundRecordsReq> implements UserOrderRefundRecordsService {}