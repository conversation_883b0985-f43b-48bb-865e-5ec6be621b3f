/*
 * Copyright (c) 2022-present <PERSON><PERSON><PERSON> Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.system.mapper;

import org.apache.ibatis.annotations.Mapper;
import cn.com.necloud.admin.system.model.entity.NoticeLogDO;
import top.continew.starter.data.mp.base.BaseMapper;

/**
 * 公告日志 Mapper
 *
 * <AUTHOR>
 * @since 2025/5/18 19:17
 */
@Mapper
public interface NoticeLogMapper extends BaseMapper<NoticeLogDO> {
}
