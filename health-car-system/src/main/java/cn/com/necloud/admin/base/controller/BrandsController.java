/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.base.controller;

import cn.com.necloud.admin.base.model.query.BrandsQuery;
import cn.com.necloud.admin.base.model.req.BrandsReq;
import cn.com.necloud.admin.base.model.resp.BrandsDetailResp;
import cn.com.necloud.admin.base.model.resp.BrandsResp;
import cn.com.necloud.admin.base.service.BrandsService;
import cn.com.necloud.admin.common.controller.BaseController;
import top.continew.starter.extension.crud.enums.Api;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.web.bind.annotation.*;

import top.continew.starter.extension.crud.annotation.CrudRequestMapping;

/**
 * 品牌列表管理 API
 * 车型库主页面查询
 * 
 * <AUTHOR>
 * @since 2025/07/10 20:06
 */
@Tag(name = "车辆品牌列表管理 API")
@RestController
@CrudRequestMapping(value = "/generator/brands", api = {Api.PAGE, Api.GET, Api.CREATE, Api.UPDATE, Api.DELETE,
    Api.EXPORT})
public class BrandsController extends BaseController<BrandsService, BrandsResp, BrandsDetailResp, BrandsQuery, BrandsReq> {}