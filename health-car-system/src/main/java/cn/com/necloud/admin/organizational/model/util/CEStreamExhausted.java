/*
 * Copyright (c) 2022-present <PERSON><PERSON><PERSON> Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package cn.com.necloud.admin.organizational.model.util;

/**
 * @Author: zxs
 * @CreateTime: 2025-07-14
 * @Description:
 * @Version: 1.8
 */

import java.io.IOException;

public class CEStreamExhausted extends IOException {
    static final long serialVersionUID = -5889118049525891904L;

    public CEStreamExhausted() {
    }
}