<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>top.continew</groupId>
        <artifactId>continew-starter</artifactId>
        <version>2.12.2</version>
    </parent>

    <groupId>cn.com.necloud</groupId>
    <artifactId>healthcar</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>

    <modules>
        <module>health-car-server</module>
        <module>health-car-system</module>
        <module>health-car-plugin</module>
        <module>health-car-common</module>
        <module>health-car-extension</module>
    </modules>

    <properties>
        <!-- 项目版本号 -->
        <revision>4.0.0-SNAPSHOT</revision>
    </properties>

    <!-- 全局依赖版本管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- API 模块（存放 Controller 层代码，打包部署的模块） -->
            <dependency>
                <groupId>cn.com.necloud</groupId>
                <artifactId>health-car-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 系统管理模块（存放系统管理模块相关功能，例如：部门管理、角色管理、用户管理等） -->
            <dependency>
                <groupId>cn.com.necloud</groupId>
                <artifactId>health-car-system</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 公共模块（存放公共工具类，公共配置等） -->
            <dependency>
                <groupId>cn.com.necloud</groupId>
                <artifactId>health-car-common</artifactId>
                <version>${revision}</version>
            </dependency>



            <!-- 能力开放插件（后续会改造为独立插件） -->
            <dependency>
                <groupId>cn.com.necloud</groupId>
                <artifactId>health-car-plugin-open</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 代码生成器插件（后续会改造为独立插件） -->
            <dependency>
                <groupId>cn.com.necloud</groupId>
                <artifactId>health-car-plugin-generator</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- Hutool（小而全的 Java 工具类库，通过静态方法封装，降低相关 API 的学习成本，提高工作效率，使 Java 拥有函数式语言般的优雅，让 Java 语言也可以"甜甜的"） -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <!-- Lombok（在 Java 开发过程中用注解的方式，简化了 JavaBean 的编写，避免了冗余和样板式代码，让编写的类更加简洁） -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional> <!-- 表示依赖不会被传递 -->
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.1.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.3.1</version>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>3.3.11</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <compilerArgument>-parameters</compilerArgument>
                </configuration>
            </plugin>
            <!-- 单元测试相关插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!-- 跳过单元测试 -->
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!-- 代码格式化插件 -->
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>apply</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <java>
                        <removeUnusedImports/>
                        <eclipse>
                            <file>.style/p3c-codestyle.xml</file>
                        </eclipse>
                        <licenseHeader>
                            <file>.style/license-header</file>
                        </licenseHeader>
                    </java>
                </configuration>
            </plugin>
            <!-- 统一版本号插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- Sonar 代码质量分析 -->
        <profile>
            <id>sonar</id>
            <properties>
                <sonar.host.url>https://sonarcloud.io</sonar.host.url>
                <sonar.organization>charles7c</sonar.organization>
                <sonar.projectKey>Charles7c_continew-admin</sonar.projectKey>
                <sonar.moduleKey>${project.groupId}:${project.artifactId}</sonar.moduleKey>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.sonarsource.scanner.maven</groupId>
                        <artifactId>sonar-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sonar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <!-- 依赖仓库配置 -->
    <repositories>
        <repository>
            <id>huawei-mirror</id>
            <name>HuaweiCloud Mirror</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </repository>
        <repository>
            <id>ali-mirror</id>
            <name>AliYun Mirror</name>
            <url>https://maven.aliyun.com/repository/public/</url>
        </repository>
    </repositories>

    <!-- 插件仓库配置 -->
    <pluginRepositories>
        <pluginRepository>
            <id>huawei-mirror</id>
            <name>HuaweiCloud Mirror</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </pluginRepository>
        <pluginRepository>
            <id>ali-mirror</id>
            <name>AliYun Mirror</name>
            <url>https://maven.aliyun.com/repository/public/</url>
        </pluginRepository>
    </pluginRepositories>
</project>
