# healthcar 健康用车集成平台

<a href="https://github.com/continew-org/continew-admin" title="Release" target="_blank">
<img src="https://img.shields.io/badge/SNAPSHOT-v4.0.0-%23ff3f59.svg" alt="Release" />
</a>
<a href="https://github.com/continew-org/continew-starter" title="ContiNew Starter" target="_blank">
<img src="https://img.shields.io/badge/ContiNew Starter-2.12.2-%236CB52D.svg" alt="ContiNew Starter" />
</a>
<a href="https://spring.io/projects/spring-boot" title="Spring Boot" target="_blank">
<img src="https://img.shields.io/badge/Spring Boot-3.3.11-%236CB52D.svg?logo=Spring-Boot" alt="Spring Boot" />
</a>
<a href="https://openjdk.org/" title="Open JDK" target="_blank">
<img src="https://img.shields.io/badge/Open JDK-21-%236CB52D.svg?logo=OpenJDK&logoColor=FFF" alt="Open JDK" />
</a>
<a href="https://www.MySQL.org/" title="MySQL" target="_blank">
<img src="https://img.shields.io/badge/MySQL-16-%23336791.svg?logo=MySQL&logoColor=FFF" alt="MySQL" />
</a>
<a href="https://app.codacy.com/gh/continew-org/continew-admin/dashboard?utm_source=gh&utm_medium=referral&utm_content=&utm_campaign=Badge_grade" title="Codacy" target="_blank">
<img src="https://app.codacy.com/project/badge/Grade/19e3e2395d554efe902c3822e65db30e" alt="Codacy" />
</a>
<a href="https://sonarcloud.io/summary/new_code?id=Charles7c_continew-admin" title="Sonar" target="_blank">
<img src="https://sonarcloud.io/api/project_badges/measure?project=Charles7c_continew-admin&metric=alert_status" alt="Sonar" />

## 简介

healthcar 健康用车系统集成平台

当前采用的技术栈：Spring Boot 3（JDK 21）、MySQL、Vue3 & Arco Design & TS & Vite、Sa-Token、MyBatis Plus、Redisson、JetCache、JustAuth、Crane4j、EasyExcel、Liquibase、Hutool 等。

## 技术特点

### 🚀 现代化技术栈
- **JDK 21**: 采用最新的长期支持版本，享受最新的语言特性和性能优化
- **MySQL**: 功能强大的开源关系数据库，支持复杂查询和高并发场景
- **Spring Boot 3**: 基于最新的Spring生态，提供更好的性能和开发体验

### 🔧 数据库配置
项目已配置连接到阿里云MySQL和Redis实例：
- **MySQL**: 支持复杂业务逻辑和事务处理
- **Redis**: 提供高性能缓存和会话存储
- **Liquibase**: 数据库版本管理，支持多环境数据库迁移

## 快速开始

> [!TIP]

```bash
# 1.克隆本项目
# 2.在 IDE（IntelliJ IDEA/Eclipse）中打开本项目
# 3.环境要求
# - JDK 21+
# - MySQL 16+
# - Redis 7+

# 4.修改配置文件中的数据源配置信息、Redis 配置信息、邮件配置信息等
# [4.也可以在 IntelliJ IDEA 中直接配置程序启动环境变量（DB_HOST、DB_PORT、DB_USER、DB_PWD、DB_NAME；REDIS_HOST、REDIS_PORT、REDIS_USER、REDIS_PWD、REDIS_DB）]

# 5.启动程序
# 启动成功，在控制台末尾会输出 healthcar Admin service started successfully.
# 并输出 API 地址及 API 接口文档地址
```

## 项目结构

> [!TIP]
> 后端采用按功能拆分模块的开发方式，下方项目目录结构是按照模块的层次顺序进行介绍的，实际 IDE 中 `continew-admin-common` 模块会因为字母排序原因排在上方。

```
healthcar-admin
├─ health-car-server（打包部署模块）
│  ├─ src
│  │  ├─ main
│  │  │  ├─ java/cn/com/necloud/admin
│  │  │  │  ├─ config （配置）
│  │  │  │  ├─ controller
│  │  │  │  │  ├─ common（通用相关 API）
│  │  │  │  │  └─ monitor（系统监控相关 API）
│  │  │  │  ├─ job （定时任务）
│  │  │  │  └─ healthcarAdminApplication.java（healthcar Admin 启动程序）
│  │  │  └─ resources
│  │  │     ├─ config（核心配置目录）
│  │  │     │  ├─ application-dev.yml（开发环境配置文件）
│  │  │     │  ├─ application-prod.yml（生产环境配置文件）
│  │  │     │  └─ application.yml（通用配置文件）
│  │  │     ├─ db/changelog（Liquibase 数据脚本配置目录）
│  │  │     │  ├─ mysql（MySQL 数据库初始 SQL 脚本目录）
│  │  │     │  ├─ MySQL（MySQL 数据库初始 SQL 脚本目录）
│  │  │     │  └─ db.changelog-master.yaml（Liquibase 变更记录文件）
│  │  │     ├─ templates（模板配置目录，例如：邮件模板）
│  │  │     ├─ banner.txt（Banner 配置文件）
│  │  │     └─ logback-spring.xml（日志配置文件）
│  │  └─ test（测试相关代码目录）
│  └─ pom.xml（包含打包相关配置）
├─ health-car-system（系统管理模块，存放系统管理相关业务功能，例如：部门管理、角色管理、用户管理等）
│  ├─ src
│  │  ├─ main
│  │  │  ├─ java/cn/com/necloud/admin
│  │  │  │  ├─ auth（系统认证相关业务）
│  │  │  │  │  ├─ controller（系统认证相关 API）
│  │  │  │  │  ├─ service（系统认证相关业务接口及实现类）
│  │  │  │  │  ├─ model（系统认证相关模型）
│  │  │  │  │  │  ├─ query（系统认证相关查询条件）
│  │  │  │  │  │  ├─ req（系统认证相关请求对象（Request））
│  │  │  │  │  │  └─ resp（系统认证相关响应对象（Response））
│  │  │  │  │  ├─ enums（系统认证相关枚举）
│  │  │  │  │  ├─ handler（系统认证相关处理器）
│  │  │  │  │  └─ config（系统认证相关配置）
│  │  │  │  └─ system（系统管理相关业务）
│  │  │  │     ├─ controller（系统管理相关 API）
│  │  │  │     ├─ service（系统管理相关业务接口及实现类）
│  │  │  │     ├─ mapper（系统管理相关 Mapper）
│  │  │  │     ├─ model（系统管理相关模型）
│  │  │  │     │  ├─ entity（系统管理相关实体对象）
│  │  │  │     │  ├─ query（系统管理相关查询条件）
│  │  │  │     │  ├─ req（系统管理相关请求对象（Request））
│  │  │  │     │  └─ resp（系统管理相关响应对象（Response））
│  │  │  │     ├─ enums（系统管理相关枚举）
│  │  │  │     ├─ util（系统管理相关工具类）
│  │  │  │     ├─ validation（系统管理相关参数校验工具类）
│  │  │  │     └─ config（系统管理相关配置）
│  │  │  └─ resources
│  │  │     └─ mapper（系统管理相关 Mapper XML 文件目录）
│  │  └─ test（测试相关代码目录）
│  └─ pom.xml
├─ health-car-plugin（插件模块，存放代码生成、任务调度等扩展模块，后续会进行插件化改造）
│  ├─ health-car-plugin-schedule（任务调度插件模块）
│  │  ├─ src
│  │  │  ├─ main/java/cn/com/necloud/admin/schedule
│  │  │  │  ├─ controller（任务调度相关 API）
│  │  │  │  ├─ service（代码生成器相关业务接口及实现类）
│  │  │  │  ├─ api（任务调度中心相关 API）
│  │  │  │  ├─ model（任务调度相关模型）
│  │  │  │  │  ├─ query（任务调度相关查询条件）
│  │  │  │  │  ├─ req（任务调度相关请求对象（Request））
│  │  │  │  │  └─ resp（任务调度相关响应对象（Response））
│  │  │  │  ├─ constant（任务调度相关常量）
│  │  │  │  ├─ enums（任务调度相关枚举）
│  │  │  │  ├─ exception（任务调度相关异常）
│  │  │  │  └─ config（任务调度相关配置）
│  │  │  └─ test（测试相关代码目录）
│  │  └─ pom.xml
│  ├─ health-car-plugin-open（能力开放插件模块）
│  │  ├─ src
│  │  │  ├─ main/java/cn/com/necloud/admin/open
│  │  │  │  ├─ controller（能力开放相关 API）
│  │  │  │  ├─ service（能力开放相关业务接口及实现类）
│  │  │  │  ├─ mapper（能力开放相关 Mapper）
│  │  │  │  ├─ model（能力开放相关模型）
│  │  │  │  │  ├─ entity（能力开放相关实体对象）
│  │  │  │  │  ├─ query（能力开放相关查询条件）
│  │  │  │  │  ├─ req（能力开放相关请求对象（Request））
│  │  │  │  │  └─ resp（能力开放相关响应对象（Response））
│  │  │  │  ├─ util（能力开放相关工具类）
│  │  │  │  └─ config（能力开放相关配置）
│  │  │  └─ test（测试相关代码目录）
│  │  └─ pom.xml
│  ├─ health-car-plugin-generator（代码生成器插件模块）
│  │  ├─ src
│  │  │  ├─ main
│  │  │  │  ├─ java/cn/com/necloud/admin/generator
│  │  │  │  │  ├─ controller（代码生成器相关 API）
│  │  │  │  │  ├─ service（代码生成器相关业务接口及实现类）
│  │  │  │  │  ├─ mapper（代码生成器相关 Mapper）
│  │  │  │  │  ├─ model（代码生成器相关模型）
│  │  │  │  │  │  ├─ entity（代码生成器相关实体对象）
│  │  │  │  │  │  ├─ query（代码生成器相关查询条件）
│  │  │  │  │  │  ├─ req（代码生成器相关请求对象（Request））
│  │  │  │  │  │  └─ resp（代码生成器相关响应对象（Response））
│  │  │  │  │  ├─ enums（代码生成器相关枚举）
│  │  │  │  │  └─ config（代码生成器相关配置）
│  │  │  │  └─ resources
│  │  │  │     └─ templates（代码生成相关模板目录）
│  │  │  │       ├─ backend（后端模板目录）
│  │  │  │       └─ frontend（前端模板目录）
│  │  │  └─ test（测试相关代码目录）
│  │  └─ pom.xml
│  └─ pom.xml
├─ health-car-common（公共模块，存放公共工具类，公共配置等）
│  ├─ src
│  │  ├─ main/java/cn/com/necloud/admin/common
│  │  │  ├─ controller（控制器基类）
│  │  │  ├─ service（公共服务接口）
│  │  │  ├─ model（公共模型）
│  │  │  │  ├─ dto（公共 DTO（Data Transfer Object））
│  │  │  │  ├─ req（公共请求对象（Request））
│  │  │  │  └─ resp（公共响应对象（Response））
│  │  │  ├─ context（公共上下文）
│  │  │  ├─ constant（公共常量）
│  │  │  ├─ enums（公共枚举）
│  │  │  ├─ util（公共工具类）
│  │  │  └─ config（公共配置）
│  │  │    ├─ mybatis（MyBatis Plus 配置）
│  │  │    ├─ websocket（Websocket 配置）
│  │  │    ├─ doc（接口文档配置）
│  │  │    ├─ excel（Excel 配置）
│  │  │    └─ exception（全局异常处理）
│  │  └─ test（测试相关代码目录）
│  └─ pom.xml
├─ health-car-extension（扩展模块）
│  ├─ health-car-extension-schedule-server（任务调度服务端模块，实际开发时如果是公司统一提供环境，可直接删除本模块）
│  │  ├─ src
│  │  │  ├─ main
│  │  │  │  ├─ java/cn/com/necloud/admin/extension/schedule
│  │  │  │  │  └─ ScheduleServerApplication.java（任务调度服务端启动程序）
│  │  │  │  └─ resources
│  │  │  │     ├─ config（核心配置目录）
│  │  │  │     │  ├─ application-dev.yml（开发环境配置文件）
│  │  │  │     │  ├─ application-prod.yml（生产环境配置文件）
│  │  │  │     │  └─ application.yml（通用配置文件）
│  │  │  │     ├─ db/changelog（Liquibase 数据脚本配置目录）
│  │  │  │     │  ├─ mysql（MySQL 数据库初始 SQL 脚本目录）
│  │  │  │     │  ├─ MySQL（MySQL 数据库初始 SQL 脚本目录）
│  │  │  │     │  └─ db.changelog-master.yaml（Liquibase 变更记录文件）
│  │  │  │     └─ logback-spring.xml（日志配置文件）
│  │  │  └─ test（测试相关代码目录）
│  │  └─ pom.xml
│  └─ pom.xml
├─ .github（GitHub 相关配置目录，实际开发时直接删除）
├─ .idea
│  └─ icon.png（IDEA 项目图标，实际开发时直接删除）
├─ .image（截图目录，实际开发时直接删除）
├─ .style（代码格式、License文件头相关配置目录，实际开发时根据需要取舍，删除时注意删除 /pom.xml 中的 spotless 插件配置）
├─ .gitignore（Git 忽略文件相关配置文件）
├─ docker（项目部署相关配置目录，实际开发时可备份后直接删除）
├─ LICENSE（开源协议文件）
├─ CHANGELOG.md（更新日志文件，实际开发时直接删除）
├─ README.md（项目 README 文件，实际开发时替换为真实内容）
├─ lombok.config（Lombok 全局配置文件）
└─ pom.xml（包含版本锁定及全局插件相关配置）
```

